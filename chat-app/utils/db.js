// 创建表 addTab('chatDb','chatData_' + uni.getStorageSync('userId'),'groupID','messageId','messgae')
/*
*	id消息id
	typecode消息类型1好友消息，2群组消息，3通知消息  4取消通话
	typecode2消息内容类型0文本，1音频，2图片，3视频，4转发消息，5撤回 （撤回消息不在界面显示，只把对应的那条消息改为撤回）
	toid接受方id 如果是群组的话就是群组id
	formid发送方id
	t消息时间
	isRedRead 是否已读
	idDel 是否撤回
	msg加密消息 撤回消息{ret:撤回的消息id，other：自定义}   转发消息：{proxy：{消息体}}

	chatid聊天对象id	只能是toid或者fromid(不能是自己的id)

	聊天记录只在数据库存
	vuex中存消息通知和聊天列表的数据（本地持久化）
	聊天列表数据格式（
		1，消息监听中监听到的消息查一下聊天列表里有没当前好友的数据，没有的话插入一条数据进入聊天列表，有的话更新一下最后一条相关信息和未读数量
		2，进入对应的聊天对象对话框后，对应的未读数量变更为0。（如果当前就在某个聊天框里，即使新来数据也直接把未读设置为0这个感觉有点问题吧）
		3，接受到加好友申请通知，并且同意之后应该也要插入一条数据
	）
	[
		{
			聊天对象id，
			聊天对象头像，聊天对象昵称，聊天对象电话（这三个在每次获取好友信息的时候需要更新一下）
			未读消息数量，
			最后一条消息内容，
			最后一条消息时间，
			最后一条消息类型（typecode2）
		}
	]

*/

export const name = 'chatDb';
export const tabName = () => 'chatData_' + uni.getStorageSync('userId');

// 创建数据库
export const openDb = () => {
	//如果数据库存在则打开，不存在则创建。
	return new Promise((resolve, reject) => {
		// 先检查数据库是否已经打开
		const isDbOpen = isOpen(name);
		console.log('数据库是否已打开:', isDbOpen);

		if (isDbOpen) {
			console.log('数据库已经打开，无需重复打开');
			resolve(true);
			return;
		}

		plus.sqlite.openDatabase({
			name: name, //数据库名称
			path: `_doc/${name}.db`, //数据库地址
			success(e) {
				console.log('数据库打开成功:', e);
				resolve(true);
			},
			fail(e) {
				console.error('数据库打开失败:', e);
				reject(false);
			}
		});
	});
}

//关闭数据库
export const closeSQL = () => {
	return new Promise((resolve, reject) => {
		plus.sqlite.closeDatabase({
			name: name,
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}
// 创建表
// 动态生成创建表的 SQL 语句
const getCreateTableSQL = () => `CREATE TABLE IF NOT EXISTS ${tabName()} (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    typecode INTEGER,
    typecode2 INTEGER,
    toid TEXT,
    fromid TEXT,
    chatid TEXT,
    t TEXT,
    isRedRead INTEGER,
    idDel INTEGER,
    msg TEXT,
    senderAvatar TEXT,
    senderNickname TEXT
)`

// 添加删除表的方法
export const dropTable = () => {
    return new Promise((resolve, reject) => {
        const tableName = tabName()
        const sql = `DROP TABLE IF EXISTS ${tableName}`
        console.log('删除表SQL:', sql)
        plus.sqlite.executeSql({
            name: name,
            sql: sql,
            success: (e) => {
                console.log('表删除成功:', e)
                resolve(true)
            },
            fail: (e) => {
                console.error('表删除失败:', e)
                reject(e)
            }
        })
    })
}

// 检查表是否包含新字段
const checkTableColumns = () => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: name,
			sql: `PRAGMA table_info(${tabName()})`,
			success(result) {
				// console.log('表结构信息:', result);
				const columns = result.map(col => col.name);
				const hasSenderAvatar = columns.includes('senderAvatar');
				const hasSenderNickname = columns.includes('senderNickname');
				resolve({ hasSenderAvatar, hasSenderNickname });
			},
			fail(e) {
				console.error('检查表结构失败:', e);
				resolve({ hasSenderAvatar: false, hasSenderNickname: false });
			}
		});
	});
};

// 修改 addTab 方法
export const addTab = async () => {
    try {
        // 先检查表是否存在
        const tableExists = await isTable(name, tabName());
        
        if (tableExists) {
            // 表存在，检查是否包含新字段
            const { hasSenderAvatar, hasSenderNickname } = await checkTableColumns();
            
            if (!hasSenderAvatar || !hasSenderNickname) {
                console.log('表结构需要升级，删除旧表并重新创建');
                // 删除旧表
                await new Promise((dropResolve, dropReject) => {
                    plus.sqlite.executeSql({
                        name: name,
                        sql: `DROP TABLE IF EXISTS ${tabName()}`,
                        success: dropResolve,
                        fail: dropReject
                    });
                });
            }
        }
        
        // 创建新表
        return new Promise((resolve, reject) => {
            const sql = getCreateTableSQL()
            // console.log('创建表SQL:', sql)
            plus.sqlite.executeSql({
                name: name,
                sql: sql,
                success: (e) => {
                    console.log('表创建成功:', e)
                    resolve(true)
                },
                fail: (e) => {
                    console.error('表创建失败:', e)
                    reject(e)
                }
            })
        })
    } catch (e) {
        console.error('addTab异常:', e)
        return Promise.reject(e)
    }
}
// 添加数据
export const addTabItem = (item) => {
	if (!item) {
		return Promise.reject(new Error('参数不能为空'));
	}
	const {id, typecode, typecode2, toid, fromid, chatid, t, isRedRead, idDel, msg, senderAvatar, senderNickname} = item;

	const safeNumber = (value, defaultValue = 0) => {
		const num = Number(value);
		return isNaN(num) ? defaultValue : num;
	};

	const safeString = (value, defaultValue = '') => {
		if (value === null || value === undefined) return defaultValue;
		return String(value);
	};

	// 使用字符串拼接避免类型不匹配问题
	const processedMsg = typeof msg === 'object' ? JSON.stringify(msg) : safeString(msg);
	const processedTime = t || new Date().toISOString();

	// 处理所有字段
	const processedId = safeNumber(id);
	const processedTypecode = safeNumber(typecode, 1);
	const processedTypecode2 = safeNumber(typecode2, 0);
	const processedToid = safeString(toid);
	const processedFromid = safeString(fromid);
	const processedChatid = safeString(chatid);
	const processedIsRedRead = safeNumber(isRedRead, 0);
	const processedIdDel = safeNumber(idDel, 0);

	// console.log('addTabItem - 原始参数:', item);
	// console.log('addTabItem - 处理后的字段:', {
	// 	id: processedId,
	// 	typecode: processedTypecode,
	// 	typecode2: processedTypecode2,
	// 	toid: processedToid,
	// 	fromid: processedFromid,
	// 	chatid: processedChatid,
	// 	t: processedTime,
	// 	isRedRead: processedIsRedRead,
	// 	idDel: processedIdDel,
	// 	msg: processedMsg
	// });

	// 验证必要字段
	if (processedId === 0 || !processedToid || !processedFromid || !processedChatid) {
		console.error('必要字段缺失或无效:', {
			id: processedId,
			toid: processedToid,
			fromid: processedFromid,
			chatid: processedChatid
		});
		return Promise.reject(new Error('必要字段缺失或无效'));
	}

	// 处理发送者信息字段
	const processedSenderAvatar = safeString(senderAvatar);
	const processedSenderNickname = safeString(senderNickname);
	let sqlStr=''
	sqlStr = `INSERT OR REPLACE INTO ${tabName()}
		(id, typecode, typecode2, toid, fromid, chatid, t, isRedRead, idDel, msg, senderAvatar, senderNickname)
		VALUES (${processedId}, ${processedTypecode}, ${processedTypecode2}, '${processedToid}', '${processedFromid}', '${processedChatid}', '${processedTime}', ${processedIsRedRead}, ${processedIdDel}, '${processedMsg.replace(/'/g, "''")}', '${processedSenderAvatar.replace(/'/g, "''")}', '${processedSenderNickname.replace(/'/g, "''")}')`;

	
	// 语音通话的拒绝消息
	if(processedTypecode2 == 12){
		sqlStr=`update ${tabName()} set msg= '${processedMsg.replace(/'/g, "''")}' , typecode2 =12 where id = ${processedId}`
		
	}
	console.log('执行SQL:', sqlStr);
	return new Promise((resolve, reject) => {
		plus.sqlite.executeSql({
			name: name,
			sql: sqlStr,
			success(e) {
				console.log('添加数据成功:', e);
				plus.sqlite.selectSql({
					name: name,
					sql: `SELECT * FROM ${tabName()} WHERE id = ${Number(id)}`,
					success(result) {
						// console.log('查询插入数据成功:', result);
						resolve(result);
					},
					fail(error) {
						console.error('查询插入数据失败:', error);
						resolve(e);
					}
				});
			},
			fail(e) {
				console.error('添加数据失败:', e);
				reject(e);
			}
		});
	});
};
// 查询所有数据表名
export const getTable = (name) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: name,
			sql: "select * FROM sqlite_master where type='table'",
			success(e) {
				resolve(e);
			},
			fail(e) {
				console.log(e)
				reject(e);
			}
		})
	})
}
// 删除表
export const deleteTable = (name,tabName) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: name,
			sql: "DROP TABLE IF EXISTS "+tabName,
			success(e) {
				resolve(e);
			},
			fail(e) {
				console.log(e)
				reject(e);
			}
		})
	})
}
// 查询表数据总条数
export const getCount = (name, tabName) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: name,
			sql: "select count(*) as num from " + tabName,
			success(e) {
				resolve(e);
			},
			fail(e) {
				reject(e);
			}
		})
	})
}

// 查询表是否存在
export const isTable = (name,tabName) => {
	return new Promise((resolve, reject) => {
		plus.sqlite.selectSql({
			name: name,
			sql: `select count(*) as isTable FROM sqlite_master where type='table' and name='${tabName}'`,
			success(e) {
				resolve(e[0].isTable ? true : false);
			},
			fail(e) {
				console.log(e)
				reject(e);
			}
		})
	})
}
// 修改数据
export const updateSQL = (name, tabName, setData, setName, setVal) => {
	if (JSON.stringify(setData) !== '{}') {
		let dataKeys = Object.keys(setData)
		let setStr = ''
		dataKeys.forEach((item, index) => {
			console.log(setData[item])
			setStr += (
				`${item} = ${JSON.stringify(setData[item])}${dataKeys.length - 1 !== index ? "," : ""}`)
		})

		return new Promise((resolve, reject) => {
			plus.sqlite.executeSql({
				name: name,
				sql: `update ${tabName} set ${setStr} where ${setName} = "${setVal}"`,
				success(e) {
					resolve(e);
				},
				fail(e) {
					console.log(e)
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => {
			reject("错误")
		});
	}
}

//删除数据库数据
export const deleteInformationType = (name,tabName,setData) => {
	if (JSON.stringify(setData) !== '{}') {
		let dataKeys = Object.keys(setData)
		let setStr = ''
		dataKeys.forEach((item, index) => {
			console.log(setData[item])
			setStr += (
				`${item}=${JSON.stringify(setData[item])}${dataKeys.length - 1 !== index ? " and " : ""}`)
		})
		return new Promise((resolve, reject) => {
			plus.sqlite.executeSql({
				name: name,
				sql: `delete from ${tabName} where ${setStr}`,
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => {
			reject("错误")
		});
	}
}



//监听数据库是否开启
export const isOpen = (name) => {
	let open = plus.sqlite.isOpenDatabase({
		name: name,
		path: `_doc/${name}.db`,
	})
	return open;
}


// 合并数据
export const mergeSql = (name,tabName,tabs) => {
	if (!tabs || tabs.length == 0) {
		return new Promise((resolve, reject) => {
			reject("错误")
		})
	}
	let itemValStr = ''
	tabs.forEach((item, index) => {
		let itemKey = Object.keys(item)
		let itemVal = ''
		itemKey.forEach((key, i) => {
			if (itemKey.length - 1 == i) {
				if (typeof item[key] == 'object') {
					itemVal += (`'${JSON.stringify(item[key])}'`)
				} else {
					itemVal += (`'${item[key]}'`)
				}
			} else {
				if (typeof item[key] == 'object') {
					itemVal += (`'${JSON.stringify(item[key])}',`)
				} else {
					itemVal += (`'${item[key]}',`)
				}
			}
		})
		if (tabs.length - 1 == index) {
			itemValStr += ('(' + itemVal + ')')
		} else {
			itemValStr += ('(' + itemVal + '),')
		}
	})
	let keys = Object.keys(tabs[0])
	let keyStr = keys.toString()
	return new Promise((resolve, reject) => {
		plus.sqlite.executeSql({
			name: name,
			sql: `insert or ignore into ${tabName} (${keyStr}) values ${itemValStr}`,
			success(e) {
				resolve(e);
			},
			fail(e) {
				console.log(e)
				reject(e);
			}
		})
	})
}
// 获取分页数据库数据
export const getDataList = async (name, tabName, num, size, chatId, byName, byType) => {
	let count = 0
	let sql = ''
	let numindex = 0
	await getCount(name, tabName).then((resNum) => {
		count = Math.ceil(resNum[0].num / size)
	})
	if(((num - 1) * size) == 0) {
	    numindex = 0
	} else {
		numindex = ((num - 1) * size) + 1
	}

	if (chatId) {
		sql = `select * from ${tabName} where chatid = '${chatId}'`
	} else {
		sql = `select * from ${tabName}`
	}

	if(byName && byType) {
		// desc asc
		sql += ` order by ${byName} ${byType}`
	}
	sql += ` limit ${numindex},${size}`
	console.log('getDataList SQL:', sql)

	if (count < num - 1) {
		return new Promise((resolve, reject) => {
			reject("无数据")
		});
	} else {
		return new Promise((resolve, reject) => {
			plus.sqlite.selectSql({
				name: name,
				sql: sql,
				success(e) {
					resolve(e);
				},
				fail(e) {
					reject(e);
				}
			})
		})
	}
}
//查询数据库数据
export const selectDataList = (name,tabName,setData,byName,byType) => {
	let setStr = ''
	let sql = ''
	if (JSON.stringify(setData) !== '{}') {
		let dataKeys = Object.keys(setData)
		dataKeys.forEach((item, index) => {
			console.log(setData[item])
			setStr += (
				`${item}=${JSON.stringify(setData[item])}${dataKeys.length - 1 !== index ? " and " : ""}`)
		})
		sql = `select * from ${tabName} where ${setStr}`
	} else {
		sql = `select * from ${tabName}`
	}
	if(byName && byType) {
		// desc asc
		sql += ` order by ${byName} ${byType}`
	}
	console.log(sql)
	if (tabName !== undefined) {
		return new Promise((resolve, reject) => {
			plus.sqlite.selectSql({
				name: name,
				sql: sql,
				success(e) {
					resolve(e);
				},
				fail(e) {
					console.log(e)
					reject(e);
				}
			})
		})
	} else {
		return new Promise((resolve, reject) => {
			reject("错误")
		});
	}
}

// 根据chatId获取聊天消息
export const getChatMessages = (chatId, page = 1, size = 20) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始获取聊天消息 ======');
            console.log('输入参数:', { chatId, page, size });
            if (!chatId) {
                throw new Error('chatId is required');
            }

            page = parseInt(page) || 1;
            size = parseInt(size) || 20;

            if (page < 1 || size < 1) {
                throw new Error('Invalid page or size parameters');
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            const offset = (page - 1) * size;

            const sql = `
                SELECT * FROM ${tabName()}
                WHERE chatid = '${chatId.toString()}'
                ORDER BY t DESC, id DESC
                LIMIT ${size} OFFSET ${offset}
            `.trim();

            console.log('执行SQL查询:', sql);
            console.log('查询的表:', tabName());

            // 先获取总记录数
            const countSql = `SELECT COUNT(*) as total FROM ${tabName()} WHERE chatid = '${chatId.toString()}'`;
            const countResult = await new Promise((resolve, reject) => {
                plus.sqlite.selectSql({
                    name: name,
                    sql: countSql,
                    success(e) {
                        resolve(e[0] ? e[0].total : 0);
                    },
                    fail(e) {
                        reject(e);
                    }
                });
            });

            console.log('符合条件的记录总数:', countResult);

            plus.sqlite.selectSql({
                name: name,
                sql: sql,
                success(e) {
                    console.log('====== 查询结果 ======');
                    console.log('记录数量:', e.length);
                    console.log('记录详情:', JSON.stringify(e, null, 2));
                    console.log('====== 查询结束 ======');
                    resolve(e);
                },
                fail(e) {
                    console.error('查询聊天记录失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('获取聊天消息处理错误:', error);
            reject(error);
        }
    });
};

// 标记消息为已读
export const markMessagesAsRead = (chatId) => {
    return new Promise((resolve, reject) => {
        const sql = `UPDATE ${tabName()} SET isRedRead = 1 WHERE chatid = '${chatId.toString()}' AND isRedRead = 0`;

        console.log('标记已读SQL:', sql);

        plus.sqlite.executeSql({
            name: name,
            sql: sql,
            success: (result) => {
                console.log('标记消息已读成功:', result);
                resolve(true);
            },
            fail: (error) => {
                console.error('标记消息已读失败:', error);
                reject(error);
            }
        });
    });
};

export const debugDatabase = () => {
	return new Promise(async (resolve, reject) => {
		try {
			console.log('====== 数据库调试信息 ======');

			const isDbOpen = isOpen(name);
			console.log('数据库是否打开:', isDbOpen);

			if (!isDbOpen) {
				await openDb();
				console.log('已打开数据库');
			}

			const tables = await getTable(name);
			console.log('数据库中的表:', tables);

			const tableExists = await isTable(name, tabName());
			console.log('表是否存在:', tableExists);

			if (tableExists) {
				const countResult = await getCount(name, tabName());
				console.log('表中的记录数:', countResult[0].num);

				if (countResult[0].num > 0) {
					const allData = await selectDataList(name, tabName(), {}, 't', 'desc');
					console.log('表中数据', allData.slice(0, 20));
				} else {
					console.log('表中暂无数据');
				}
			}

			console.log('====== 数据库调试完成 ======');
			resolve({
				isDbOpen,
				tables,
				tableExists,
				recordCount: tableExists ? (await getCount(name, tabName()))[0].num : 0
			});
		} catch (error) {
			console.error('数据库调试时出错:', error);
			reject(error);
		}
	});
}

// 生成测试数据并插入到数据库
export const generateTestData = () => {
	return new Promise(async (resolve, reject) => {
		try {

			const isDbOpen = isOpen(name);
			if (!isDbOpen) {
				await openDb();
			}

			await addTab();

			const testData = [
				// 好友消息 - 文本类型
				{
					id: 1001,
					typecode: 1, // 好友消息
					typecode2: 0, // 文本
					toid: 10001, // 接收方ID
					fromid: 10002, // 发送方ID
					chatid: '10002', // 聊天对象ID（对于10001来说，聊天对象是10002）
					t: '2023-10-01 09:15:22',
					isRedRead: 1, // 已读
					idDel: 0, // 未撤回
					msg: '你好，最近怎么样？'
				},
				{
					id: 1002,
					typecode: 1,
					typecode2: 0,
					toid: 10002,
					fromid: 10001,
					chatid: '10002', // 聊天对象ID（对于10001来说，聊天对象是10002）
					t: '2023-10-01 09:16:45',
					isRedRead: 1,
					idDel: 0,
					msg: '我很好，谢谢关心！你呢？'
				},
				{
					id: 1003,
					typecode: 1,
					typecode2: 0,
					toid: 10001,
					fromid: 10002,
					chatid: '10002', // 聊天对象ID（对于10001来说，聊天对象是10002）
					t: '2023-10-01 09:17:30',
					isRedRead: 1,
					idDel: 0,
					msg: '我也不错，最近在忙一个新项目'
				},

				// 好友消息 - 图片类型
				{
					id: 1004,
					typecode: 1,
					typecode2: 2, // 图片
					toid: 10001,
					fromid: 10002,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-01 10:05:12',
					isRedRead: 1,
					idDel: 0,
					msg: '这是我们公司的新产品图片'
				},

				// 好友消息 - 音频类型
				{
					id: 1005,
					typecode: 1,
					typecode2: 1, // 音频
					toid: 10002,
					fromid: 10001,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-01 10:15:45',
					isRedRead: 1,
					idDel: 0,
					msg: '给你发个语音消息，听一下'
				},

				// 群组消息 - 文本
				{
					id: 2001,
					typecode: 2, // 群组消息
					typecode2: 0, // 文本
					toid: 20001, // 群组ID
					fromid: 10001, // 发送者ID
					chatid: '20001', // 群组聊天ID
					t: '2023-10-02 14:22:36',
					isRedRead: 1,
					idDel: 0,
					msg: '大家好，我是新来的成员'
				},
				{
					id: 2002,
					typecode: 2,
					typecode2: 0,
					toid: 20001,
					fromid: 10003,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-02 14:23:12',
					isRedRead: 1,
					idDel: 0,
					msg: '欢迎新成员！'
				},
				{
					id: 2003,
					typecode: 2,
					typecode2: 0,
					toid: 20001,
					fromid: 10002,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-02 14:24:05',
					isRedRead: 1,
					idDel: 0,
					msg: '我们下周会有个小型聚会，欢迎参加'
				},

				// 群组消息 - 图片类型
				{
					id: 2004,
					typecode: 2,
					typecode2: 2, // 图片
					toid: 20001,
					fromid: 10003,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-02 14:30:56',
					isRedRead: 1,
					idDel: 0,
					msg: '这是上次活动的照片'
				},

				// 群组消息 - 视频类型
				{
					id: 2005,
					typecode: 2,
					typecode2: 3, // 视频
					toid: 20001,
					fromid: 10004,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-02 15:12:33',
					isRedRead: 0, // 未读
					idDel: 0,
					msg: '分享一个视频教程'
				},

				// 通知消息 - 好友申请
				{
					id: 3001,
					typecode: 3, // 通知消息
					typecode2: 0, // 文本
					toid: 10001,
					fromid: 10005,
					chatid: '10005', // 通知聊天ID
					t: '2023-10-03 08:45:21',
					isRedRead: 1,
					idDel: 0,
					msg: '我是你的同学，想加你为好友'
				},

				// 通知消息 - 群邀请
				{
					id: 3002,
					typecode: 3,
					typecode2: 0,
					toid: 10002,
					fromid: 10003,
					chatid: '10003', // 通知聊天ID
					t: '2023-10-03 09:22:15',
					isRedRead: 0, // 未读
					idDel: 0,
					msg: '邀请你加入"技术交流群"'
				},

				// 好友消息 - 撤回的消息
				{
					id: 1006,
					typecode: 1,
					typecode2: 5, // 撤回
					toid: 10001,
					fromid: 10002,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-04 16:32:45',
					isRedRead: 1,
					idDel: 1, // 已撤回
					msg: '{"ret":1005,"other":"用户撤回了一条消息"}'
				},

				// 好友消息 - 转发消息
				{
					id: 1007,
					typecode: 1,
					typecode2: 4, // 转发
					toid: 10003,
					fromid: 10001,
					chatid: '10003', // 聊天对象ID
					t: '2023-10-04 17:01:23',
					isRedRead: 1,
					idDel: 0,
					msg: '{"proxy":{"id":2001,"msg":"大家好，我是新来的成员"}}'
				},

				// 更多好友消息
				{
					id: 1008,
					typecode: 1,
					typecode2: 0,
					toid: 10001,
					fromid: 10002,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-05 09:33:12',
					isRedRead: 1,
					idDel: 0,
					msg: '周末有空一起打球吗？'
				},
				{
					id: 1009,
					typecode: 1,
					typecode2: 0,
					toid: 10002,
					fromid: 10001,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-05 09:35:45',
					isRedRead: 1,
					idDel: 0,
					msg: '有空，几点？'
				},
				{
					id: 1010,
					typecode: 1,
					typecode2: 0,
					toid: 10001,
					fromid: 10002,
					chatid: '10002', // 聊天对象ID
					t: '2023-10-05 09:36:18',
					isRedRead: 0, // 未读
					idDel: 0,
					msg: '下午2点，老地方见'
				},

				// 更多群组消息
				{
					id: 2006,
					typecode: 2,
					typecode2: 0,
					toid: 20001,
					fromid: 10001,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-05 14:52:36',
					isRedRead: 0, // 未读
					idDel: 0,
					msg: '项目进度会议定在明天上午10点'
				},
				{
					id: 2007,
					typecode: 2,
					typecode2: 0,
					toid: 20001,
					fromid: 10004,
					chatid: '20001', // 群组聊天ID
					t: '2023-10-05 14:55:02',
					isRedRead: 0, // 未读
					idDel: 0,
					msg: '收到，我会准时参加'
				}
			];

			// 测试数据
			for (const item of testData) {
				try {
					await addTabItem(item);
					console.log(`成功插入数据ID: ${item.id}`);
				} catch (error) {
					console.error(`插入数据ID ${item.id} 失败:`, error);
				}
			}

			console.log('====== 测试数据生成完成 ======');
			resolve({
				success: true,
				count: testData.length
			});
		} catch (error) {
			console.error('生成测试数据时出错:', error);
			reject(error);
		}
	});
}

/**
 * 搜索聊天消息
 * @param {string} keyword - 搜索关键词
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 * @returns {Promise} 搜索结果
 */
export const searchChatMessages = (keyword, page = 1, size = 20) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始搜索聊天消息 ======');
            console.log('搜索参数:', { keyword, page, size });

            if (!keyword || keyword.trim() === '') {
                resolve([]);
                return;
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            page = parseInt(page) || 1;
            size = parseInt(size) || 20;
            const offset = (page - 1) * size;

            // 搜索消息内容，排除撤回的消息
            const sql = `
                SELECT * FROM ${tabName()}
                WHERE msg LIKE '%${keyword.trim()}%'
                AND idDel = 0
                AND typecode2 = 0
                ORDER BY t DESC
                LIMIT ${size} OFFSET ${offset}
            `.trim();

            console.log('搜索SQL:', sql);

            plus.sqlite.selectSql({
                name: name,
                sql: sql,
                success(e) {
                    console.log('搜索消息结果:', e.length, '条');
                    console.log('搜索结果详情:', JSON.stringify(e, null, 2));
                    resolve(e);
                },
                fail(e) {
                    console.error('搜索聊天消息失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('搜索聊天消息处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 搜索聊天列表（从store中搜索）
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 匹配的聊天列表
 */
export const searchChatList = (keyword) => {
    try {
        if (!keyword || keyword.trim() === '') {
            return [];
        }

        const chatList = uni.getStorageSync('chatList') || {};
        const searchKey = keyword.trim().toLowerCase();
        const results = [];

        Object.values(chatList).forEach(chat => {
            // 搜索昵称或最后一条消息
            const nickname = (chat.nickname || '').toLowerCase();
            const lastMessage = (chat.lastMessage || '').toLowerCase();

            if (nickname.includes(searchKey) || lastMessage.includes(searchKey)) {
                results.push({
                    chatId: chat.chatid,
                    name: chat.nickname || chat.phone || '未知',
                    avatar: chat.avatar || '/static/My/avatar.jpg',
                    lastMessage: chat.lastMessage || '',
                    time: chat.timestamp,
                    unreadCount: chat.unreadCount || 0,
                    type: chat.typecode === 2 ? 'group' : 'contact'
                });
            }
        });

        // 按时间倒序排列
        results.sort((a, b) => new Date(b.time) - new Date(a.time));

        console.log('搜索聊天列表结果:', results);
        return results;
    } catch (error) {
        console.error('搜索聊天列表失败:', error);
        return [];
    }
};

/**
 * 获取所有聊天对象（用于搜索联系人和群组）
 * @returns {Promise} 聊天对象列表
 */
export const getAllChatContacts = () => {
    return new Promise(async (resolve, reject) => {
        try {
            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            // 获取所有不同的聊天对象
            const sql = `
                SELECT DISTINCT chatid,
                       MAX(t) as lastTime,
                       (SELECT msg FROM ${tabName()} t2 WHERE t2.chatid = t1.chatid ORDER BY t DESC LIMIT 1) as lastMessage,
                       (SELECT typecode FROM ${tabName()} t3 WHERE t3.chatid = t1.chatid ORDER BY t DESC LIMIT 1) as typecode
                FROM ${tabName()} t1
                WHERE idDel = 0
                GROUP BY chatid
                ORDER BY lastTime DESC
            `.trim();

            console.log('获取聊天对象SQL:', sql);

            plus.sqlite.selectSql({
                name: name,
                sql: sql,
                success(e) {
                    console.log('聊天对象查询结果:', e.length, '个');

                    // 结合本地存储的聊天列表信息
                    const chatList = uni.getStorageSync('chatList') || {};
                    const results = e.map(item => {
                        const chatInfo = chatList[item.chatid] || {};
                        return {
                            chatId: item.chatid,
                            name: chatInfo.nickname || chatInfo.phone || `用户${item.chatid}`,
                            avatar: chatInfo.avatar || '/static/My/avatar.jpg',
                            lastMessage: item.lastMessage || '',
                            lastTime: item.lastTime,
                            type: item.typecode === 2 ? 'group' : 'contact'
                        };
                    });

                    resolve(results);
                },
                fail(e) {
                    console.error('获取聊天对象失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('获取聊天对象处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 撤回消息 - 更新数据库中的消息状态
 * @param {number} messageId - 要撤回的消息ID
 * @param {string} recallContent - 撤回消息的显示内容
 * @returns {Promise} 撤回结果
 */
export const recallMessageInDB = (messageId, recallContent) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始撤回消息 ======');
            console.log('消息ID:', messageId);
            console.log('撤回内容:', recallContent);

            if (!messageId) {
                throw new Error('messageId is required');
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            // 更新消息状态：设置为撤回状态，更新消息内容和类型
            const sql = `
                UPDATE ${tabName()}
                SET typecode2 = 5,
                    idDel = 1,
                    msg = '${recallContent.replace(/'/g, "''")}'
                WHERE id = ${messageId}
            `.trim();

            console.log('撤回消息SQL:', sql);

            plus.sqlite.executeSql({
                name: name,
                sql: sql,
                success(e) {
                    console.log('撤回消息成功:', e);
                    resolve({
                        success: true,
                        messageId: messageId,
                        message: '消息撤回成功'
                    });
                },
                fail(e) {
                    console.error('撤回消息失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('撤回消息处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 根据消息ID获取消息详情
 * @param {number} messageId - 消息ID
 * @returns {Promise} 消息详情
 */
export const getMessageById = (messageId) => {
    return new Promise(async (resolve, reject) => {
        try {
            if (!messageId) {
                throw new Error('messageId is required');
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            const sql = `SELECT * FROM ${tabName()} WHERE id = ${messageId}`;

            console.log('查询消息SQL:', sql);

            plus.sqlite.selectSql({
                name: name,
                sql: sql,
                success(e) {
                    console.log('查询消息结果:', e);
                    if (e.length > 0) {
                        resolve(e[0]);
                    } else {
                        reject(new Error('消息不存在'));
                    }
                },
                fail(e) {
                    console.error('查询消息失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('查询消息处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 清空聊天记录
 * @param {string} chatId - 聊天对象ID
 * @returns {Promise} 清空结果
 */
export const clearChatMessages = (chatId) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始清空聊天记录 ======');
            console.log('聊天ID:', chatId);
            if (!chatId) {
                throw new Error('chatId is required');
            }
            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }
            // 查询要删除的记录数量
            const countSql = `SELECT COUNT(*) as total FROM ${tabName()} WHERE chatid = '${chatId.toString()}'`;
            const countResult = await new Promise((resolve, reject) => {
                plus.sqlite.selectSql({
                    name: name,
                    sql: countSql,
                    success(e) {
                        resolve(e[0] ? e[0].total : 0);
                    },
                    fail(e) {
                        reject(e);
                    }
                });
            });

            console.log('将要删除的记录数:', countResult);

            // 删除指定chatId的所有记录
            const deleteSql = `DELETE FROM ${tabName()} WHERE chatid = '${chatId.toString()}'`;
            console.log('执行删除SQL:', deleteSql);
            plus.sqlite.executeSql({
                name: name,
                sql: deleteSql,
                success(e) {
                    console.log('清空聊天记录成功:', e);
                    console.log('删除的记录数:', countResult);
                    resolve({
                        success: true,
                        deletedCount: countResult,
                    });
                },
                fail(e) {
                    console.error('清空聊天记录失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('清空聊天记录处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 清空所有聊天记录
 * @returns {Promise} 清空结果
 */
export const clearAllChatMessages = () => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始清空所有聊天记录 ======');

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            // 查询总记录数
            const countSql = `SELECT COUNT(*) as total FROM ${tabName()}`;
            const countResult = await new Promise((resolve, reject) => {
                plus.sqlite.selectSql({
                    name: name,
                    sql: countSql,
                    success(e) {
                        resolve(e[0] ? e[0].total : 0);
                    },
                    fail(e) {
                        reject(e);
                    }
                });
            });

            console.log('将要删除的总记录数:', countResult);

            // 删除所有记录
            const deleteSql = `DELETE FROM ${tabName()}`;

            console.log('执行删除SQL:', deleteSql);

            plus.sqlite.executeSql({
                name: name,
                sql: deleteSql,
                success(e) {
                    console.log('清空所有聊天记录成功:', e);
                    console.log('删除的记录数:', countResult);
                    resolve({
                        success: true,
                        deletedCount: countResult,
                    });
                },
                fail(e) {
                    console.error('清空所有聊天记录失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('清空所有聊天记录处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 删除单条消息
 * @param {number} messageId - 消息ID
 * @returns {Promise} 删除结果
 */
export const deleteMessage = (messageId) => {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('====== 开始删除单条消息 ======');
            console.log('消息ID:', messageId);

            if (!messageId) {
                throw new Error('messageId is required');
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }

            // 删除指定ID的消息
            const deleteSql = `DELETE FROM ${tabName()} WHERE id = ${messageId}`;
            console.log('执行删除SQL:', deleteSql);

            plus.sqlite.executeSql({
                name: name,
                sql: deleteSql,
                success(e) {
                    console.log('删除消息成功:', e);
                    resolve({
                        success: true,
                        message: '消息删除成功'
                    });
                },
                fail(e) {
                    console.error('删除消息失败:', e);
                    reject(e);
                }
            });
        } catch (error) {
            console.error('删除消息处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 撤回消息 - 将消息标记为撤回状态
 * @param {number} messageId - 消息ID
 * @param {Object} messageObj - 消息对象（可选）
 * @returns {Promise} 撤回结果
 */
export const recallMessage = (messageId, messageObj) => {
    return new Promise(async (resolve, reject) => {
        try {
            if (!messageId) {
                throw new Error('messageId is required');
            }

            // 检查数据库是否打开
            if (!isOpen(name)) {
                console.log('数据库未打开，正在打开...');
                await openDb();
            }
			// 根据messageId 查询消息详情

			const res = await getMessageById(messageId);
			console.log('撤回消息详情+++++++++:', res);
			// 获取当前用户ID
			const currentUserId = uni.getStorageSync('userId');
			// 当前用户id等于fromid 则是发送者撤回 否则是接收者撤回
			const isSelfRecall = res.fromid == currentUserId;
			
			if (isSelfRecall) {
				// 自己撤回消息
				const recallText = "你撤回了一条消息";
				await updateRecallMessageInDB(messageId, recallText);
				resolve({
					success: true,
					messageId: messageId,
					message: '消息撤回成功',
					recallText: recallText
				});
			} else {
				// 对方撤回消息，需要获取撤回者的用户信息
				try {
					// 动态导入GetUserByID函数
					const { GetUserByID } = await import('../api/api.js');
					
					const userRes = await GetUserByID(res.fromid);
					console.log('获取撤回用户信息结果:', userRes.data);
					
					let recallText = "对方撤回了一条消息"; // 默认文本
					
					if (userRes.data.code == 0 && userRes.data.data) {
						// 获取用户信息成功，使用用户名
						const userName = userRes.data.data.name || userRes.data.data.iphone_num || '未知用户';
						recallText = `"${userName}"撤回了一条消息`;
					} else {
						console.error('获取撤回用户信息失败:', userRes.data);
					}
					
					await updateRecallMessageInDB(messageId, recallText);
					resolve({
						success: true,
						messageId: messageId,
						message: '消息撤回成功',
						recallText: recallText
					});
				} catch (userError) {
					console.error('获取撤回用户信息失败:', userError);
					// 获取用户信息失败，使用默认文本
					const recallText = "对方撤回了一条消息";
					await updateRecallMessageInDB(messageId, recallText);
					resolve({
						success: true,
						messageId: messageId,
						message: '消息撤回成功',
						recallText: recallText
					});
				}
			}
        } catch (error) {
            console.error('撤回消息处理错误:', error);
            reject(error);
        }
    });
};

/**
 * 更新数据库中的撤回消息
 * @param {number} messageId - 消息ID
 * @param {string} recallText - 撤回文本
 * @returns {Promise} 更新结果
 */
const updateRecallMessageInDB = (messageId, recallText) => {
	return new Promise((resolve, reject) => {
		// 更新消息状态为撤回
		const updateSql = `UPDATE ${tabName()} SET typecode2 = 5, idDel = 1, msg = "${recallText.replace(/"/g, '\"')}" WHERE id = ${messageId}`;
		console.log('撤回SQL:', updateSql);

		plus.sqlite.executeSql({
			name: name,
			sql: updateSql,
			success(e) {
				console.log('撤回消息成功:', e);
				resolve(e);
			},
			fail(e) {
				console.error('撤回消息失败:', e);
				reject(e);
			}
		});
	});
};

// 删除语音通话发起消息（用于通话消息合并）
export const deleteVoiceCallMessage = (chatid, fromid, toid) => {
	return new Promise((resolve, reject) => {
		// 删除同一聊天中的语音通话发起消息（typecode2=9）
		// 需要考虑双向聊天的情况
		const sql = `DELETE FROM ${tabName()} WHERE chatid = '${chatid}' AND typecode2 = 9 AND ((fromid = '${fromid}' AND toid = '${toid}') OR (fromid = '${toid}' AND toid = '${fromid}'))`;

		console.log('删除语音通话发起消息SQL:', sql);

		plus.sqlite.executeSql({
			name: name,
			sql: sql,
			success: (data) => {
				console.log('删除语音通话发起消息成功:', data);
				resolve(data);
			},
			fail: (error) => {
				console.error('删除语音通话发起消息失败:', error);
				reject(error);
			}
		});
	});
};

/**
 * 通过接收消息的ID来查询chatId
 * @param {String} toid
 */
export function getChatIdByToId(toid) {
	return new Promise((resolve, reject) => {
		if(!toid) {
			reject(new Error('toid is required'));
			return;
		}
		// 检查数据库是否打开
		if (!isOpen(name)) {
			console.log('数据库未打开，正在打开...');
			openDb();
		}

		const currentUserId = uni.getStorageSync('userId');
		console.log('查询chatId - currentUserId:', currentUserId, 'toid:', toid);

		// 对于私聊，使用较小的用户ID作为chatid，确保双方使用相同的chatid
		const chatid = Math.min(parseInt(currentUserId), parseInt(toid)).toString();
		console.log('计算得到的chatId:', chatid);

		// 验证是否存在与该用户的聊天记录
		const selectSql = `SELECT DISTINCT chatid FROM ${tabName()} WHERE chatid = '${chatid}' LIMIT 1`;
		console.log('验证chatId SQL:', selectSql);
		plus.sqlite.selectSql({
			name: name,
			sql: selectSql,
			success(e) {
				console.log('验证chatId结果:', e);
				if (e.length > 0) {
					// 找到了聊天记录，返回chatid
					resolve(e[0].chatid);
				} else {
					// 没有找到聊天记录，直接返回计算的chatid（用于新聊天）
					console.log('没有找到现有聊天记录，返回计算的chatId:', chatid);
					resolve(chatid);
				}
			},
			fail(e) {
				console.error('验证chatId失败:', e);
				// 即使查询失败，也返回计算的chatid
				resolve(chatid);
			}
		});
	});
}