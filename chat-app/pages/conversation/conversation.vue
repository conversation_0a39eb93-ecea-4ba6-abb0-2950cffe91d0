<template>
  <view class="chat-container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>
    <!-- 固定顶部导航栏 -->
    <view class="fixed-header" :style="{ top: statusBarHeight + 'rpx' }">
      <view class="nav-bar">
        <view class="nav-left" @click.once="goBack">
          <image class="nav-icon-img" src="/static/conversation/left.png"></image>
        </view>
        <view class="nav-title">
          <text>{{ chatTitle }}</text>
        </view>
        <view class="nav-right" @click="goToGroup">
          <image class="nav-icon-img" src="/static/conversation/dot.png"></image>
        </view>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view scroll-y="true" class="chatslist_container smooth-scroll" :style="{
      marginTop: statusBarHeight + navBarHeight + 'rpx',
      height: containerHeight,
      'overflow-anchor': 'auto'
    }" :scroll-into-view="scrollToId" :scroll-with-animation="false" id="chat-list" @scroll="onScroll"
      @scrolltoupper="onScrollToUpper" enable-flex="true" :show-scrollbar="false">

      <!-- 下拉加载提示 -->
      <view class="loading-more" v-if="isLoadingMore">
        <view class="loading-icon"></view>
        <text class="loading-text">加载历史消息中...</text>
      </view>

      <!-- 使用消息列表组件 -->
      <message-list ref="messageListComponent" :messages="displayMessages" :initialMessage="initialMessage" :isLoadingMore="isLoadingMore"
        @copy="onCopyMsg" @delete="onDeleteMsg" @forward="forwardMessage" @recall="onRecallMsg" @multiSelect="multiSelectMessages"
        @openFile="openFile" @playVoice="playVoiceMessage" @viewImage="viewImage" @playVideo="handlePlayVideo" @avatarClick="goToGroup"
		@voiceClick="startVoiceCall"
        class="hardware-accelerated" />

      <!-- 添加一个锚点元素 -->
      <view id="scroll-anchor" style="height: 1px; width: 100%"></view>

      <!-- 未读消息提示 -->
      <view v-if="hasUnread" class="unread-dot" @tap="handleUnreadClick">
        <view class="dot"></view>
        <text>有新消息</text>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-wrapper-container" :style="{ bottom: keyboardHeight > 0 ? keyboardHeight + 'rpx' : '0' }">
      <!-- 输入工具栏 -->
      <view class="toolbar">
        <view class="voice-button" @tap="toggleVoiceInput">
          <image class="tool-icon-img" :src="isVoiceMode
            ? '/static/conversation/keyboard.png'
            : '/static/conversation/micph.png'
            "></image>
        </view>

        <!-- 文本输入框/语音按钮 -->
        <view class="input-wrapper" :class="{ expanded: inputMessage && inputMessage.length > 0 }">
          <input v-if="!isVoiceMode" class="message-input" v-model="inputMessage" placeholder="输入消息..."
            confirm-type="send" @confirm="sendTextMessage(0)" @focus="onInputFocus" @blur="onInputBlur"
            @input="inputChange" :adjust-position="false" :cursor-spacing="20" ref="messageInput"
            :focus="inputFocusState" confirm-hold="true" hold-keyboard="true"
            @keyboardheightchange="onKeyboardHeightChange" />
          <view v-else class="voice-input-button" @touchstart.prevent="startRecordVoice"
            @touchend.prevent="stopRecordVoice" @touchcancel.prevent="stopRecordVoice"
            @touchmove.prevent="onTouchMove">
            <text>{{ isRecording ? "松开 结束" : "按住 说话" }}</text>
          </view>
        </view>

        <!-- 表情按钮 -->
        <view class="emoji-button" @tap="toggleEmojiPanel" :class="{ active: activePanel == 'emoji' }">
          <image class="tool-icon-img" src="/static/conversation/smile.png"></image>
        </view>

        <!-- 发送/加号按钮 -->
        <view class="send-button" v-if="inputMessage && inputMessage.trim().length > 0" @tap="sendTextMessage(0)">
          <image class="send-icon-img" src="/static/conversation/send.png"></image>
        </view>
        <view v-else class="more-button" @tap="toggleMorePanel" :class="{ active: activePanel == 'more' }">
          <image class="tool-icon-img" src="/static/conversation/add.png"></image>
        </view>
      </view>

      <!-- 扩展面板 -->
      <view class="extension-panel" v-if="activePanel == 'emoji'" @touchmove.stop.prevent>
        <!-- 表情面板 -->
        <view class="emoji-panel">
          <scroll-view class="emoji-scroll" scroll-y="true">
            <!-- 表情网格 -->
            <view class="emoji-grid">
              <view class="emoji-item" v-for="(emoji, index) in emojiList" :key="index" @tap="insertEmoji(emoji)">
                <text>{{ emoji }}</text>
              </view>
            </view>
          </scroll-view>
          <!-- 发送按钮 -->
          <view class="emoji-send-area">
            <view class="emoji-send-button" @tap="sendFromEmoji" :class="{ active: inputMessage.trim().length > 0 }">
              <text>发送</text>
            </view>
            <view class="emoji-backspace" @tap="deleteEmoji">
              <image class="emoji-delete-icon" src="/static/conversation/delete.png" />
            </view>
          </view>
        </view>
      </view>
      <view class="extension-panel" v-if="activePanel == 'more'" @touchmove.stop.prevent>
        <!-- 更多功能面板 -->
        <view class="more-panel">
          <view class="more-panel-row">
            <view class="more-panel-item" @tap="chooseImage(2)">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/photos.png"></image>
              </view>
              <text class="more-panel-title">相册</text>
            </view>
            <view class="more-panel-item" @tap="chooseImage(3)">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/photoaction.png"></image>
              </view>
              <text class="more-panel-title">拍摄</text>
            </view>
            <!-- <view class="more-panel-item" @tap="chooseVideo">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/video.png"></image>
              </view>
              <text class="more-panel-title">视频</text>
            </view> -->
            <view class="more-panel-item" @tap="startVoiceCall" v-if="sourceTypecode!=2">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/pronunciation.png"></image>
              </view>
              <text class="more-panel-title">语音</text>
            </view>
            <view class="more-panel-item" @tap="chooseImage(2)">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/files.png"></image>
              </view>
              <text class="more-panel-title">文件</text>
            </view>
          </view>
        </view>
      </view>
      <view class="voice-input-panel" v-if="isVoiceMode && isRecording">
        <!-- 语音输入内容 -->
        <view class="voice-recording-indicator">
          <view class="recording-wave-animation" v-if="!isCancelRecording"></view>
          <view class="cancel-recording-icon" v-else>
            <image src="/static/conversation/cancel.png" class="cancel-icon"></image>
          </view>
          <text class="voice-duration">{{ formatRecordDuration(recordDuration) }}</text>
          <text class="voice-tip">{{ isCancelRecording ? "松开手指，取消发送" : "手指上滑，取消发送" }}</text>
        </view>
      </view>
    </view>

    <!-- 在聊天容器末尾添加视频全屏预览组件 -->
    <!-- 视频全屏预览 -->
    <view v-if="showVideoFullscreen" class="fullscreen-video-container" @tap="closeVideoFullscreen">
      <video id="fullscreen-video" :src="currentVideo" :poster="currentVideoPoster" class="fullscreen-video" controls
        autoplay @fullscreenchange="handleVideoFullscreenChange" @ended="handleVideoEnded"></video>
      <view class="close-video-btn" @tap.stop="closeVideoFullscreen">
        <text class="close-video-icon">×</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  getCurrentInstance,
  defineExpose,
  watch
} from "vue";
import MessageList from "../../components/MessageList.vue";
import { sendMessage } from '../../utils/chatService';
import { getChatMessages, markMessagesAsRead, generateTestData, debugDatabase, deleteMessage, recallMessage, getChatIdByToId, recallMessageInDB, getMessageById } from '../../utils/db.js';
import { onLoad } from '@dcloudio/uni-app'
import store from '../../store';
import { formatTime, formatSmartTime } from '../../utils/decrypt.js';
import { GetUserByID } from '../../api/api'
import { searchUser } from '../../api/friend'


// 聊天相关的状态
const chatId = ref('');
const chatTitle = ref('');
const chatAvatar = ref('');
const pageSize = ref(20);
const hasMoreMessages = ref(true);
const isLoadingMore = ref(false);
// 添加语音模式状态
const isVoiceMode = ref(false);

// 当前播放的语音消息ID
const currentPlayingVoice = ref(null);
// 获取组件实例，用于在 createSelectorQuery 中使用
const instance = getCurrentInstance();
// 消息列表
const messageList = ref([]);
const initialMessage = ref(null);

// 是否群聊标识
const sourceTypecode = ref(null);
const isGroupChat = computed(() => sourceTypecode.value == 2);

// 上一条消息的时间
let lastMessageTime = null;
let activeVoice={}
const recorderManager = uni.getRecorderManager();
const innerAudioContext = uni.createInnerAudioContext();

innerAudioContext.autoplay = true;
innerAudioContext.onStop(res=>{
	activeVoice.isPlaying=false
})
innerAudioContext.onPause(res=>{
	activeVoice.isPlaying=false
})

/**
 * 判断是否应该显示时间标签
 */
const shouldShowTimeTag = (currentTime, isFirstMessageOfLoad = false) => {
  // isFirstMessageOfLoad: 标记是否为本次加载消息中的第一条，用于历史消息加载
  // 对于实时接收的新消息，这个通常是 false

  if (isFirstMessageOfLoad && messageList.value.length === 0) {
    // 如果是加载的第一批消息中的第一条，且当前列表为空，则显示时间
    lastMessageTime = currentTime;
    return true;
  }

  if (!lastMessageTime) {
    // 如果之前没有消息时间记录（通常是应用启动后第一条消息），则显示时间
    lastMessageTime = currentTime;
    return true;
  }

  const current = new Date(currentTime);
  const last = new Date(lastMessageTime);
  const timeDiff = current.getTime() - last.getTime();
  const fiveMinutes = 5 * 60 * 1000; // 5分钟的毫秒数

  if (timeDiff >= fiveMinutes) {
    lastMessageTime = currentTime;
    return true;
  }
  
  // 默认不更新 lastMessageTime，除非确定要显示新时间
  return false;
};

/**
 * 重新计算整个消息列表的时间标签
 * 用于加载历史消息后重新计算时间显示
 */
const recalculateTimeLabels = () => {
  if (messageList.value.length === 0) {
    lastMessageTime = null; // 如果列表为空，重置lastMessageTime
    return;
  }

  let tempLastDisplayedTime = null;

  // 确保消息按时间升序排列
  // 如果是从数据库获取并预处理的，通常已经是升序
  messageList.value.sort((a, b) => new Date(a.time) - new Date(b.time));

  messageList.value.forEach((msg, index) => {
    if (index === 0) {
      // 列表中的第一条消息总是显示时间
      msg.showTimeTag = true;
      msg.timeTag = formatSmartTime(msg.time);
      tempLastDisplayedTime = msg.time;
    } else {
      const currentTime = new Date(msg.time);
      const prevMsgTime = new Date(tempLastDisplayedTime);
      const timeDiff = currentTime.getTime() - prevMsgTime.getTime();
      const fiveMinutes = 5 * 60 * 1000;

      if (timeDiff >= fiveMinutes) {
        msg.showTimeTag = true;
        msg.timeTag = formatSmartTime(msg.time);
        tempLastDisplayedTime = msg.time;
      } else {
        msg.showTimeTag = false;
        msg.timeTag = ''; // 不显示时间时，清空timeTag
      }
    }
  });

  // 更新全局的lastMessageTime为最后一条显示了时间的消息的时间
  // 或者如果所有消息都不显示时间（理论上不可能，因为第一条总显示），则为最后一条消息时间
  if (messageList.value.length > 0) {
    const lastShownMsg = [...messageList.value].reverse().find(m => m.showTimeTag);
    lastMessageTime = lastShownMsg ? lastShownMsg.time : messageList.value[messageList.value.length - 1].time;
  }
};

// 合并语音通话消息：删除重复的语音通话发起消息，只保留最终的通话结果
const mergeVoiceCallMessages = () => {
  const messagesToRemove = [];
  // 遍历消息列表，查找语音通话终止消息
  messageList.value.forEach((message, index) => {
    if (message.type == 'voiceCallEnd') {
      // 查找对应的语音通话发起消息
      const voiceCallStartIndex = messageList.value.findIndex((msg, msgIndex) =>
        msgIndex < index && // 查找当前消息之前的消息
        msg.type == 'voiceCall' &&
        msg.self == message.self // 同一个发送者
      );

      if (voiceCallStartIndex !== -1) {
        console.log('找到需要删除的语音通话发起消息:', messageList.value[voiceCallStartIndex]);
        messagesToRemove.push(voiceCallStartIndex);
      }
    }
  });
  messagesToRemove.sort((a, b) => b - a).forEach(index => {
    messageList.value.splice(index, 1);
  });

  if (messagesToRemove.length > 0) {
    console.log(`合并语音通话消息完成，删除了 ${messagesToRemove.length} 条发起消息`);
  }
};

// 加载历史消息
const loadMessages = async (page = 1, size = 20) => {
  try {
    isLoadingMore.value = true;

    // 添加错误处理和日志
    if (!chatId.value) {
      console.error('加载消息失败：聊天ID为空');
      return;
    }

    console.log('开始加载消息，参数:', {
      chatId: chatId.value,
      page,
      size
    });

    const messages = await getChatMessages(chatId.value, page, size);
    initialMessage.value = messages;
    console.log('从数据库加载的消息:', messages);

    // 调试：检查消息是否包含senderAvatar字段
    if (messages.length > 0) {
      console.log('第一条消息的字段:', Object.keys(messages[0]));
      console.log('第一条消息的senderAvatar:', messages[0].senderAvatar);
      console.log('第一条消息的senderNickname:', messages[0].senderNickname);
    }

    // 检查消息格式
    if (!Array.isArray(messages)) {
      console.error('返回的消息格式不正确');
      return;
    }

    // 如果没有更多消息
    if (messages.length < size) {
      hasMoreMessages.value = false;
    }

    // 转换消息格式
    // 在映射前，如果这是第一页加载，则重置 lastMessageTime，确保第一条消息能正确显示时间
    if (page === 1) {
      lastMessageTime = null;
    }

    const formattedMessages = messages.map((msg, index) => {
      if (!msg || typeof msg !== 'object') {
        console.error('无效的消息格式:', msg);
        return null;
      }

      const isSelf = String(msg.fromid) === String(uni.getStorageSync('userId'));
      const typecode2 = parseInt(msg.typecode2 || 0);

      // 根据消息类型确定内容类型
      let type = 'text';
      switch (typecode2) {
        case 0: type = 'text'; break;
        case 1: type = 'voice'; break;
        case 2: type = 'image'; break;
        case 3: type = 'video'; break;
        case 4: type = 'forward'; break;
        case 5: type = 'recall'; break;
        case 9: type = 'voiceCall'; break;
        case 12: type = 'voiceCallEnd'; break;
        default: type = 'text';
      }

      // 解析消息内容
      let content = msg.msg || '';
      try {
        if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
          content = JSON.parse(content);
        }
      } catch (e) {
        console.error('解析消息内容失败:', e);
        content = msg.msg || '';
      }

      // 计算是否显示时间标签
      // 对于历史消息，第一条总是尝试显示 (isFirstMessageOfLoad = true)
      // 后续依赖recalculateTimeLabels进行修正
      const isFirstInCurrentBatch = index === 0;
      const showTimeTagInitially = shouldShowTimeTag(msg.t || new Date().toISOString(), isFirstInCurrentBatch && page === 1);
      console.log('content', content);

      // 处理消息内容，特别是撤回消息和语音通话终止消息
      let finalContent = content;
      if (type === 'recall') {
        // 撤回消息直接使用msg字段的内容
        finalContent = msg.msg || '消息已撤回';
      } else if (type === 'voiceCallEnd') {
        // 语音通话终止消息，根据apply值显示不同内容
        try {
			finalContent=msg.msg
          // const msgData = typeof msg.msg === 'string' ? JSON.parse(msg.msg) : msg.msg;
          // const apply = msgData?.apply;
          // switch (apply) {
          //   case 4:
          //     finalContent = '发起方中断通话';
          //     break;
          //   case 5:
          //     finalContent = '接收方中断通话';
          //     break;
          //   case 6:
          //     finalContent = '通话异常中断';
          //     break;
          //   default:
          //     finalContent = '通话已结束';
          // }
        } catch (e) {
          console.error('解析语音通话终止消息失败:', e);
          finalContent = '通话已结束';
        }
      } else if (content && typeof content === 'object' && content.other) {
        // 其他类型消息如果有other字段则使用other
        finalContent = content.other;
      }

      // 确定头像显示逻辑（历史消息）
      let messageAvatar;
      let messageNickname;
      console.log('处理消息头像 - 消息ID:', msg.id, '是否为群聊:', isGroupChat.value, '是否为自己:', isSelf, 'senderAvatar:', msg.senderAvatar);

      if (isSelf) {
        // 自己发送的消息使用自己的头像
        messageAvatar = uni.getStorageSync('userInfo').head_img || '/static/My/avatar.jpg';
        console.log('自己的消息，使用头像:', messageAvatar);
      } else {
        // 其他人发送的消息
        console.log('非自己的消息，处理头像逻辑...', msg);
        if (isGroupChat.value && msg.senderAvatar) {
          // 群聊中使用发送者的头像（如果有的话）
          messageAvatar = msg.senderAvatar;
          console.log('群聊消息，使用发送者头像:', messageAvatar);
        } else if (isGroupChat.value && msg.fromid) {
          // 群聊中如果没有发送者头像，尝试从存储的用户信息中获取
          // 这里可以考虑添加一个缓存机制来存储用户头像信息
          // 通过fromid从数据库中获取用户信息
            // GetUserByID(msg.fromid).then(res => {
            //   if (res.data.code == 0) {
            //     messageAvatar = res.data.data.head_img;
            //     messageNickname = res.data.data.name;
            //   }
            // });
        } else {
          // 私聊使用聊天对象头像
          messageAvatar = chatAvatar.value;
          console.log('私聊消息，使用聊天对象头像:', messageAvatar);
        }
      }

      return {
        id: msg.id || Date.now(),
        self: isSelf,
        type: type,
        content: finalContent,
        time: msg.t || new Date().toISOString(),
        // timeTag 和 showTimeTag 会在 recalculateTimeLabels 中统一处理
        timeTag: '', // 初始为空，由recalculateTimeLabels填充
        showTimeTag: false, // 初始为false，由recalculateTimeLabels决定
        avatar: messageAvatar,
        senderNickname: messageNickname, // 添加发送者昵称信息
        status: 'success',
        unread: false,
		fromid:msg.fromid
      };
    }).filter(Boolean);

    // 根据页码决定如何更新消息列表
    if (page === 1) {
      // 第一页：数据库返回的是最新消息（倒序），需要反转以正确显示（最新消息在底部）
      messageList.value = formattedMessages.reverse();
    } else {
      // 加载更多时：新获取的消息是更早的，需要反转后加到列表前面
      messageList.value = [...formattedMessages.reverse(), ...messageList.value];
    }

    // 语音通话消息合并逻辑：删除重复的语音通话发起消息
    mergeVoiceCallMessages();

    // 重新计算所有消息的时间标签
    recalculateTimeLabels();

    console.log('消息加载完成，当前消息列表长度:', messageList.value.length);

    // 如果是第一页，滚动到底部
    if (page === 1) {
      nextTick(() => {
        scrollToBottom();
      });
    } else {
      // 如果是加载更多，保持当前滚动位置，或者根据需要调整
      // 这里可以记录加载前的位置，加载后尝试恢复，但通常UI会自动处理
    }
  } catch (error) {
    console.error('加载消息失败:', error);
  } finally {
    isLoadingMore.value = false;
  }
}
// 监听新消息
const setupMessageListener = () => {
    uni.$on('chat-message-received', (message) => {
        console.log('收到新消息:', message);

        // 检查消息是否属于当前聊天
        if (message.chatid == chatId.value) {
            // 判断消息是否是自己发送的
            const isSelf = message.fromid == uni.getStorageSync('userId');
			console.log('--------',uni.getStorageSync('userId'))
            // 如果是自己发送的消息，检查是否有对应的乐观更新消息
            if (isSelf) {
                // 查找可能的乐观更新消息（通过内容和时间匹配）
                const optimisticMessageIndex = messageList.value.findIndex(msg =>
                    msg.self &&
                    msg.content === message.msg &&
                    msg.status === 'sending' &&
                    Math.abs(new Date(msg.time).getTime() - new Date(message.t).getTime()) < 10000 // 10秒内
                );

                if (optimisticMessageIndex !== -1) {
                    // 更新乐观消息为真实消息
                    messageList.value[optimisticMessageIndex] = {
                        ...messageList.value[optimisticMessageIndex],
                        id: message.id,
                        time: message.t,
                        timeTag: formatSmartTime(message.t),
                        status: 'success'
                    };
                    console.log('更新乐观消息为真实消息:', message.id);
                    return;
                }
            }

            // 检查消息是否已存在（通过ID检查）
            const existingMessage = messageList.value.find(msg => msg.id == message.id);
            if (existingMessage) {
				console.log(messageList.value)
				console.log(message)
				if(message.typecode2==12){
					// messageList.value.forEach(msg=>{
					// 	if(msg.id == message.id){
					// 		msg.content=message.msg
					// 	}
					// })
					// console.log(messageList.value)
					// return
				}else{
					console.log('消息已存在，跳过添加:', message.id);
					return;
				}
                
            }

            // 根据消息类型确定内容类型
            let type = 'text';
            switch(parseInt(message.typecode2)) {
                case 0: type = 'text'; break;
                case 1: type = 'voice'; break;
                case 2: type = 'image'; break;
                case 3: type = 'video'; break;
                case 4: type = 'forward'; break;
                case 5: type = 'recall'; break;
				case 9: type = 'voiceCall'; break;
				case 12: type = 'voiceCallEnd'; break;
                default: type = 'text';
            }

            // 语音通话消息合并逻辑：如果是通话终止消息，删除之前的通话发起消息
            if (type == 'voiceCallEnd') {
                // 查找并删除同一聊天中的语音通话发起消息
                const voiceCallIndex = messageList.value.findIndex(msg =>msg.id == message.id );

                if (voiceCallIndex != -1) {
                    console.log('删除之前的语音通话发起消息:', messageList.value[voiceCallIndex]);
					messageList.value[voiceCallIndex].content=message.msg
					return
                    // messageList.value.splice(voiceCallIndex, 1);
                }
            }

            // 解析消息内容
            let content = message.msg;
            try {
                if (typeof content == 'string' && (content.startsWith('{') || content.startsWith('['))) {
                    content = JSON.parse(content);
                }
            } catch (e) {
                console.error('解析消息内容失败:', e);
                content = message.msg; // 如果解析失败，使用原始消息
            }

            // 计算是否显示时间标签
            const showTimeTag = shouldShowTimeTag(message.t);

			// 处理消息内容，特别是撤回消息和语音通话终止消息
			let finalContent = content;
			if (type == 'recall') {
				// 撤回消息直接使用msg字段的内容
				finalContent = message.msg || '消息已撤回';
			} else if (type == 'voiceCallEnd') {
				// 语音通话终止消息，根据apply值显示不同内容
				try {
					finalContent=message.msg
					// const msgData = typeof message.msg == 'string' ? JSON.parse(message.msg) : message.msg;
					// const apply = msgData?.apply;
					// switch (apply) {
					// 	case 4:
					// 		finalContent = '发起方中断通话';
					// 		break;
					// 	case 5:
					// 		finalContent = '接收方中断通话';
					// 		break;
					// 	case 6:
					// 		finalContent = '通话异常中断';
					// 		break;
					// 	default:
					// 		finalContent = '通话已结束';
					// }
				} catch (e) {
					console.error('解析语音通话终止消息失败:', e);
					finalContent = '通话已结束';
				}
			} else if (content && typeof content == 'object' && content.other) {
				// 其他类型消息如果有other字段则使用other
				finalContent = content.other;
			}

            // 确定头像显示逻辑
            let messageAvatar;
            if (isSelf) {
                // 自己发送的消息使用自己的头像
                messageAvatar = uni.getStorageSync('userInfo').head_img || '/static/My/avatar.jpg';
            } else {
                // 其他人发送的消息
                if (isGroupChat.value && message.senderAvatar) {
                    // 群聊中使用发送者的头像
                    messageAvatar = message.senderAvatar;
                } else {
                    // 私聊或没有发送者头像信息时使用聊天对象头像
                    messageAvatar = message.avatar || chatAvatar.value;
                }
            }

            const newMessage = {
                id: message.id || Date.now().toString(),
                self: isSelf,
                type: type,
                content: finalContent,
                time: message.t,
                timeTag: formatSmartTime(message.t),
                showTimeTag: showTimeTag,
                avatar: messageAvatar,
                senderNickname: message.senderNickname, // 添加发送者昵称信息
                status: 'success',
                unread: !isSelf // 如果不是自己发送的消息，则标记为未读
            };

            // 将新消息添加到列表末尾（按数据库存入顺序显示）
            messageList.value = [...messageList.value, newMessage];

            // 重新计算时间标签，确保消息顺序正确
            recalculateTimeLabels();

            // 播放提示音
            if (!isSelf) {
                const audio = uni.createInnerAudioContext();
                audio.src = '/static/notification.mp3';
                audio.play();
            }

            // 自动滚动到底部
            nextTick(() => {
                scrollToBottom();
            });

            // 标记为已读
            markMessagesAsRead(chatId.value)
                .then(() => {
                    console.log('消息已标记为已读');
                    // 更新Vuex中的未读计数
                    store.commit('updateUnreadCount', { chatId: chatId.value, count: 0 });
                })
                .catch(err => console.error('标记消息已读失败:', err));

            // 存储当前聊天ID到本地存储
            uni.setStorageSync('currentChatId', chatId.value);
			
			// 请求语音通话
			if(message.typecode2==9&&message.fromid==uni.getStorageSync("userId")){
				uni.navigateTo({
				  url: '/pages/call/voice-call?newMessage=' + JSON.stringify(newMessage)+'&toId='+message.toid+"&isCaller=true&id="+message.id,
				  success: () => {
				    console.log('成功跳转到语音通话页面');
				  },
				  fail: (err) => {
				    console.error('打开语音通话页面失败', err);
				    // 如果失败，尝试再次检查pages.json配置
				    uni.showToast({
				      title: '打开语音通话失败',
				      icon: 'none',
				      duration: 3000
				    });
				  }
				});
			}
			
        } else {
            // 更新其他聊天的未读计数
            const currentChat = store.state.chatList[message.chatid];
            const currentCount = currentChat ? currentChat.unreadCount || 0 : 0;
            store.commit('updateUnreadCount', {
                chatId: message.chatid,
                count: currentCount + 1
            });
        }
    });
};

// 在组件卸载时移除事件监听
onUnmounted(() => {
    uni.$off('chat-message-received');
    // 清除当前聊天ID
    uni.removeStorageSync('currentChatId');
});
// 页面加载时获取聊天ID和相关信息
onLoad(async (options) => {
    console.log('聊天页面参数:', options);
    sourceTypecode.value = options.sourceTypecode || null;
    toId.value = options.toId
    // 群聊、私聊
  if (!sourceTypecode.value) {
    // 私聊
    if (options.chatid) {
      chatId.value = options.chatid;
      chatTitle.value = decodeURIComponent(options.nickname || '');
      chatAvatar.value = decodeURIComponent(options.avatar || '');

      // 存储当前聊天ID到本地存储
      uni.setStorageSync('currentChatId', chatId.value);

      // 加载消息
      loadMessages(1, pageSize.value);

      // 标记消息为已读
      markMessagesAsRead(chatId.value)
        .then(() => {
          console.log('消息已标记为已读');
          // 更新Vuex中的未读计数
          store.commit('updateUnreadCount', { chatId: chatId.value, count: 0 });
        })
        .catch(err => console.error('标记消息已读失败:', err));
    } else {
      const resChatId = await getChatIdByToId(options.toId)
      chatId.value = resChatId
      console.log('通过接收者的id获取chatId.value', chatId.value)
      uni.setStorageSync('currentChatId', resChatId);
      loadMessages(1, pageSize.value);
      // 标记消息为已读
      markMessagesAsRead(resChatId)
        .then(() => {
          console.log('消息已标记为已读');
          // 更新Vuex中的未读计数
          store.commit('updateUnreadCount', { chatId: resChatId, count: 0 });
        })
        .catch(err => console.error('标记消息已读失败:', err));
      // 从好友信息中进去没有chatid，通过接收者的id获取chatid
      chatTitle.value = decodeURIComponent(options.nickname || '');
      chatAvatar.value = decodeURIComponent(options.avatar || '');
    }
  } else {
    // 群聊 - 使用群组ID作为chatid
    chatId.value = options.chatid || options.toId; // 优先使用传入的chatid，否则使用toId
    chatTitle.value = decodeURIComponent(options.nickname || '');
    chatAvatar.value = decodeURIComponent(options.avatar || '');
    
    // 存储当前聊天ID到本地存储
    uni.setStorageSync('currentChatId', chatId.value);
    
    // 加载群聊消息
    loadMessages(1, pageSize.value);
    
    // 标记群聊消息为已读
    markMessagesAsRead(chatId.value)
      .then(() => {
        console.log('群聊消息已标记为已读');
        // 更新Vuex中的未读计数
        store.commit('updateUnreadCount', { chatId: chatId.value, count: 0 });
      })
      .catch(err => console.error('标记群聊消息已读失败:', err));
  }
});

// 在组件挂载时设置消息监听
onMounted(() => {
    setupMessageListener();

    // 监听聊天记录清空事件
    uni.$on('chatHistoryCleared', (data) => {
        console.log('收到聊天记录清空事件:', data);
        if (data.chatid === chatId.value) {
            // 清空当前消息列表
            messageList.value = [];
            console.log('当前聊天记录已清空，消息列表已重置');

            // 显示提示
            uni.showToast({
                title: '聊天记录已清空',
                icon: 'none'
            });
        }
    });

    // 监听撤回消息事件
    uni.$on('message-recalled', (data) => {
        console.log('收到撤回消息事件:', data);
        if (data.chatid === chatId.value) {
            // 在当前消息列表中找到被撤回的消息并更新
            const messageIndex = messageList.value.findIndex(msg => msg.id == data.messageId);
            if (messageIndex !== -1) {
                messageList.value[messageIndex] = {
                    ...messageList.value[messageIndex],
                    type: 'recall',
                    content: data.recallText,
                    recalled: true
                };
                console.log('消息列表中的撤回消息已更新');
            }
        }
    });
});

// 页面卸载时移除事件监听
onUnmounted(() => {
    uni.$off('chatHistoryCleared');
    uni.$off('message-recalled');
});

// 调试函数：生成测试数据
const generateTestDataForDebug = async () => {
    try {
        console.log('开始生成测试数据...');
        const result = await generateTestData();
        console.log('测试数据生成完成:', result);

        // 生成完成后重新加载消息
        await loadMessages(1, pageSize.value);

        uni.showToast({
            title: '测试数据生成成功',
            icon: 'success'
        });
    } catch (error) {
        console.error('生成测试数据失败:', error);
        uni.showToast({
            title: '生成测试数据失败',
            icon: 'none'
        });
    }
};

// 调试函数：查看数据库状态
const debugDatabaseStatus = async () => {
    try {
        console.log('开始调试数据库...');
        const result = await debugDatabase();
        console.log('数据库调试结果:', result);

        uni.showModal({
            title: '数据库状态',
            content: `表存在: ${result.tableExists}\n记录数: ${result.recordCount}`,
            showCancel: false
        });
    } catch (error) {
        console.error('数据库调试失败:', error);
        uni.showToast({
            title: '数据库调试失败',
            icon: 'none'
        });
    }
};


// 接受者Id
const toId = ref(0);

// 输入相关
const inputMessage = ref("");
const isRecording = ref(false);
const isCancelRecording = ref(false);
const recordStartTime = ref(0);
const recordDuration = ref(0);
const recordTimer = ref(null);
const touchStartY = ref(0);
const inputFocusState = ref(false); // 默认不自动聚焦

// 面板相关
const activePanel = ref(null); // 可选值: null, 'emoji', 'more', 'voice'
// 微信的表情面板高度约为屏幕的1/3
const panelHeight = ref(500); // rpx单位，微信中约为屏幕高度的1/3
const keyboardHeight = ref(0);
const toolbarHeight = ref(100); // toolbar的固定高度

// 计算输入区域总高度
const inputTotalHeight = computed(() => {
  const baseToolbarHeight = toolbarHeight.value; // 100rpx

  // 表情面板或更多面板显示时
  if (activePanel.value) {
    return baseToolbarHeight + panelHeight.value;
  }

  // 键盘弹出时
  if (keyboardHeight.value > 0) {
    return baseToolbarHeight + keyboardHeight.value;
  }

  // 默认只有工具栏
  return baseToolbarHeight;
});
// 导航栏高度
const navBarHeight = ref(88); // 44px -> 88rpx
const statusBarHeight = ref(0);
// 存储容器实际高度（px单位）
const containerHeight = ref('70vh');

// 滚动相关
const scrollTop = ref(0);
const isScrolling = ref(false);
const isAutoScrolling = ref(false);
const scrollTimer = ref(null);

// 使用标志位来避免scrollTop反馈循环
const isUserScrolling = ref(false);  // 用户是否在主动滚动
const isProgrammaticScrolling = ref(false);  // 程序是否在主动滚动

// 引入节流相关变量
let throttleTimer = null; // 用于节流的计时器
const throttleDelay = 100; // 缩短节流延迟，提高响应速度

// 上拉加载相关（已移除oldestMessageId，直接使用页码计算）

// 未读消息提示
const hasUnread = ref(false);
const isAtBottom = ref(true);

// 输入框引用
const messageInput = ref(null);

// 表情数据
const emojiList = ref([
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "🤗",
]);

// 添加新的响应式变量
const scrollToId = ref("");

// 添加视频相关状态
const showVideoFullscreen = ref(false);
const currentVideo = ref("");
const currentVideoPoster = ref("");

// 添加 MessageList 组件的引用
const messageListComponent = ref(null);

// 显示限制，最多显示50条消息，防止内存过多
const MAX_MESSAGES = 30;

// 计算实际展示的消息列表（限制条数）
const displayMessages = computed(() => {
  if (messageList.value.length <= MAX_MESSAGES) {
    return messageList.value;
  }

  // 只显示最近的MAX_MESSAGES条消息
  return messageList.value.slice(messageList.value.length - MAX_MESSAGES);
});

// 生命周期
onMounted(() => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.screenWidth || 375; // 获取屏幕宽度，用于rpx转换

    // 设置状态栏高度
    statusBarHeight.value = systemInfo.statusBarHeight * (750 / screenWidth); // px转rpx

    // 初始计算容器高度
    const screenHeight = systemInfo.windowHeight;
    const statusBarHeightPx = systemInfo.statusBarHeight;
    const navBarHeightPx = uni.upx2px(navBarHeight.value);
    const inputHeightPx = uni.upx2px(inputTotalHeight.value);

    // 设置为确切的像素值，避免使用calc()
    containerHeight.value = `${screenHeight - statusBarHeightPx - navBarHeightPx - inputHeightPx}px`;

    // 添加加载完成后的滚动，但使用延迟确保组件已渲染
    setTimeout(() => {
      ensureScrollToBottom();
    }, 300);

    // 只在非H5平台监听键盘高度
    if (systemInfo.platform !== "h5") {
      try {
        // 监听键盘高度变化
        uni.onKeyboardHeightChange((res) => {
          keyboardHeight.value = res.height * 2 + 25; // 转换为rpx
          console.log("键盘高度变化:", keyboardHeight.value);

          // 如果键盘收起，且当前没有激活的面板
          if (keyboardHeight.value == 0 && !activePanel.value) {
            // 不执行任何滚动操作
            return;
          }

          // 延迟执行滚动，等待布局稳定
          setTimeout(() => {
            ensureScrollToBottom();
          }, 300);
        });
      } catch (error) {
        console.warn("键盘高度监听失败:", error);
      }
    }
  } catch (e) {
    console.error("获取系统信息失败:", e);
    statusBarHeight.value = 44; // 备用值

    // 同样添加初始滚动，使用更可靠的方法
    setTimeout(() => {
      ensureScrollToBottom();
    }, 300);
  }
});
onUnmounted(() => {
  // 只在非H5平台移除监听
  if (uni.getSystemInfoSync().platform !== "h5") {
    try {
      uni.offKeyboardHeightChange();
    } catch (error) {
      console.warn("移除键盘高度监听失败:", error);
    }
  }

  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }
});

// 方法
const onInputFocus = () => {
  // 设置输入框为聚焦状态
  inputFocusState.value = true;

  // 微信的行为是：聚焦输入框时，关闭所有面板
  if (activePanel.value) {
    activePanel.value = null;
  }

  // 确保消息在视图底部可见
  setTimeout(() => {
    scrollToBottom(true);
  }, 300);
};
const goToGroup = (e) => {
  const chatID = chatId.value || toId.value;

  // 构建跳转参数，包含聊天类型信息
  const params = {
    toId: toId.value,
    chatId: chatID,
    sourceTypecode: sourceTypecode.value || 1, // 传递聊天类型
    nickname: encodeURIComponent(chatTitle.value || ''),
    avatar: encodeURIComponent(chatAvatar.value || '')
  };
  if(sourceTypecode.value==2){
	  console.log('uni.$u.store.state.friendList',store.state.friendList)
	  // 判断是不是好友
	  let isFriend=store.state.friendList.find(item=>{
		  if(item.Friend==e.fromid){
			  return item
		  }
	  })
	  if(isFriend){
		  uni.navigateTo({
		    url: `/pages/friend/info?Friend=${e.fromid}`
		  });
	  }else{
		  searchUser({t:0,like:false,id:Number(e.fromid)}).then(res=>{
			  console.log(res.data.data.user)
			  
			  if(res.data.code==0){
				  let user=res.data.data.user[0]
				  console.log(`/pages/addFriends/index?id=${user.ID}&nickname=${user.name}&phone=${user.iphone_num}&avatar=${user.head_img}`)
				  uni.navigateTo({
				      url: `/pages/addFriends/index?id=${user.ID}&nickname=${user.name}&phone=${user.iphone_num}&avatar=${user.head_img}`
				  })
			  }else{
				  uni.showToast({
				      title: '跳转失败失败',
				      icon: 'none'
				  });
			  }
			  
		  })
	  }
	  
	  return
  }
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/profile/profile?${queryString}`
  });
}

const goToVoice=()=>{
	
}
// 删除表情
const deleteEmoji = () => {
  if (inputMessage.value.length == 0) return;

  try {
    // 更智能的方法处理emoji表情删除
    const stringArray = Array.from(inputMessage.value);
    stringArray.pop();  // 删除最后一个字符（无论是普通字符还是emoji）
    inputMessage.value = stringArray.join('');
  } catch (e) {
    console.error('删除表情失败', e);
    // 兼容性处理，出错时简单删除最后一个字符
    inputMessage.value = inputMessage.value.slice(0, -1);
  }
};

// 监听新消息
const receiveMessage = (message) => {
  messageList.value.push(message);
  if (!isAtBottom.value) {
    hasUnread.value = true;
  } else {
    nextTick(() => scrollToBottom(true));
  }
};

// 切换更多功能面板
const toggleMorePanel = () => {
  // 确保语音模式关闭
  if (isVoiceMode.value) {
    isVoiceMode.value = false;
  }

  const currentKeyboardHeight = keyboardHeight.value;

  if (activePanel.value == "more") {
    // 关闭更多面板，聚焦输入框（就像微信一样）
    activePanel.value = null;

    // 延迟聚焦，避免面板关闭和键盘弹出的动画冲突
    setTimeout(() => {
      inputFocusState.value = true;
      try {
        if (messageInput.value) {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 150);
  } else {
    // 关闭键盘，显示更多面板
    activePanel.value = "more";
    inputFocusState.value = false;

    try {
      if (messageInput.value) {
		  console.log('messageInput.value',messageInput.value)
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }

    // 如果键盘是打开的，给它一点时间关闭
    if (currentKeyboardHeight > 0) {
      // 防止面板和键盘高度变化引起的滚动抖动
      setTimeout(() => {
        scrollToBottom(true);
      }, 300);
    } else {
      // 确保内容滚动到底部
      nextTick(() => {
        scrollToBottom(true);
      });
    }
  }
};

// 切换表情面板
const toggleEmojiPanel = () => {
  // 根据微信的交互逻辑：
  // 1. 如果表情面板已经开启，则关闭面板并聚焦输入框
  // 2. 如果更多面板开启，则切换到表情面板
  // 3. 如果键盘开启，则关闭键盘，打开表情面板
  // 4. 如果什么都没有开启，则打开表情面板

  // 确保语音模式关闭
  if (isVoiceMode.value) {
    isVoiceMode.value = false;
  }

  const currentKeyboardHeight = keyboardHeight.value;

  if (activePanel.value == "emoji") {
    // 关闭表情面板，聚焦输入框（就像微信一样）
    activePanel.value = null;

    // 延迟聚焦，避免面板关闭和键盘弹出的动画冲突
    setTimeout(() => {
      inputFocusState.value = true;
      try {
        if (messageInput.value) {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 150);
  } else {
    // 关闭键盘，显示表情面板
    activePanel.value = "emoji";
    inputFocusState.value = false;

    try {
      if (messageInput.value) {
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }

    // 如果键盘是打开的，给它一点时间关闭
    if (currentKeyboardHeight > 0) {
      // 防止面板和键盘高度变化引起的滚动抖动
      setTimeout(() => {
        scrollToBottom(true);
      }, 300);
    } else {
      // 确保内容滚动到底部
      nextTick(() => {
        scrollToBottom(true);
      });
    }
  }
};

// 切换语音输入模式
const toggleVoiceInput = () => {
  isVoiceMode.value = !isVoiceMode.value;

  // 微信的行为是：切换到语音模式时，关闭所有面板和键盘
  if (isVoiceMode.value) {
    activePanel.value = null;
    inputFocusState.value = false;
    try {
      if (messageInput.value && typeof messageInput.value.blur == 'function') {
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }
  } else {
    // 从语音模式切换到文本模式，微信的行为是聚焦输入框
    inputFocusState.value = true;
    setTimeout(() => {
      try {
        if (messageInput.value && typeof messageInput.value.focus == 'function') {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 100);
  }
};

// 返回上一页
const goBack = () => {
  // 清除当前聊天ID
  uni.removeStorageSync('currentChatId');
  uni.navigateBack();
};

const onInputBlur = () => {
  // 只改变状态，不执行其他操作，让其他函数来处理面板的状态
  inputFocusState.value = false;
};

const inputChange = () => {
  // 不执行任何操作
};

// 插入表情
const insertEmoji = (emoji) => {
  // 微信的行为是：点击表情时，保持表情面板打开，同时向输入框添加表情
  inputMessage.value += emoji;

  // 没有必要聚焦，因为表情面板打开时，输入框应该保持失焦状态

  // 微信不会主动发送表情，除非点击发送按钮
};

// 从表情面板发送消息
const sendFromEmoji = () => {
  sendTextMessage(0); // 表情消息也是文本消息，typecode2=0
};

// 发送状态管理
const sendingMessages = ref(new Set()); // 存储正在发送的消息ID

// 发送文本消息
//typecode2消息内容类型0文本，1音频，2图片，3视频，4转发消息，5撤回 （撤回消息不在界面显示，只把对应的那条消息改为撤回）
//msg参数应该是字符串
const sendTextMessage = async (typecode2, msg) => {
  console.log('sendTextMessage typecode2:', typecode2);
  console.log('sendTextMessage msg:', msg);

  let messageContent = '';

  // 处理不同类型的消息内容
  if (typecode2 == 0) {
    // 文本消息
    if (!inputMessage.value.trim()) return;
    messageContent = inputMessage.value;
  } else if (typecode2 == 1) {
    // 音频消息
    messageContent = msg;
  } else if (typecode2 == 2 || typecode2 == 3) {
    // 图片或视频消息
    messageContent = msg;
  }

  // 生成临时消息ID
  const tempMessageId = Date.now().toString();

  // 检查是否已经在发送中
  if (sendingMessages.value.has(tempMessageId)) {
    return;
  }


  const params = {
    id: tempMessageId, // 设置消息ID，用于去重
    fromid: uni.getStorageSync('userId'),
    toId: Number(toId.value),
    msg: messageContent,
    typecode: sourceTypecode.value ? 2 : 1,
    typecode2: typecode2,
    t: new Date().toISOString(), // 添加发送时间戳
  }
  
  // 如果是群聊消息，添加groupID参数
  if (sourceTypecode.value == 2) {
    params.groupID = Number(toId.value); // 群聊时，toId就是群组ID
  }

  // 立即添加到发送中列表
  sendingMessages.value.add(tempMessageId);

  // 立即显示发送的消息（乐观更新）
  const optimisticMessage = {
    id: tempMessageId,
    self: true,
    type: 'text',
    content: messageContent,
    time: new Date().toISOString(),
    timeTag: formatSmartTime(new Date().toISOString()),
    showTimeTag: shouldShowTimeTag(new Date().toISOString()),
    avatar: uni.getStorageSync('userAvatar') || '/static/My/avatar.jpg',
    status: 'sending', // 发送中状态
    unread: false
  };

  // 添加到消息列表
  messageList.value = [...messageList.value, optimisticMessage];
  // 清空输入框并关闭面板
  inputMessage.value = '';
  activePanel.value = null;

  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });

  try {
    // 发送消息
    await sendMessage(params);

    // 发送成功，更新消息状态
    const messageIndex = messageList.value.findIndex(msg => msg.id === tempMessageId);
    if (messageIndex !== -1) {
      messageList.value[messageIndex].status = 'success';
    }

    // 从发送中列表移除
    sendingMessages.value.delete(tempMessageId);

    // 注意：真实的消息会通过WebSocket返回，但我们需要避免重复显示
    // 在setupMessageListener中会检查消息是否已存在
  } catch (error) {
    console.error('发送消息失败:', error);

    // 发送失败，更新消息状态
    const messageIndex = messageList.value.findIndex(msg => msg.id === tempMessageId);
    if (messageIndex !== -1) {
      messageList.value[messageIndex].status = 'failed';
    }

    // 从发送中列表移除
    sendingMessages.value.delete(tempMessageId);

    uni.showToast({
      title: '发送失败',
      icon: 'none'
    });
  }
};

// 处理用户滚动事件，不更新scrollTop
const onScroll = (e) => {
  // 如果是程序触发的滚动，忽略事件处理
  if (isProgrammaticScrolling.value) {
    return;
  }

  // 标记用户正在滚动
  isUserScrolling.value = true;

  // 只记录当前滚动位置，但不更新scrollTop.value
  const currentScrollTop = e.detail.scrollTop;

  // 清除之前的计时器
  clearTimeout(scrollTimer.value);

  // 设置计时器检测滚动结束
  scrollTimer.value = setTimeout(() => {
    isUserScrolling.value = false;

    // 滚动停止后才计算底部状态
    const scrollView = e.detail;
    const atBottom = scrollView.scrollHeight - currentScrollTop - scrollView.clientHeight < 30;

    if (atBottom !== isAtBottom.value) {
      isAtBottom.value = atBottom;
      if (atBottom) {
        hasUnread.value = false;
      }
    }
  }, 400);
};

// 完全重写滚动到底部方法，避免使用scrollTop
const scrollToBottom = (force = false) => {
  // 如果正在滚动且不是强制滚动，则跳过
  if (isUserScrolling.value && !force) {
    return;
  }

  // 标记为程序滚动，避免触发onScroll处理
  isProgrammaticScrolling.value = true;

  // 使用滚动锚点而不是设置scrollTop
  scrollToId.value = "scroll-anchor";

  // 延迟重置程序滚动标志
  setTimeout(() => {
    isProgrammaticScrolling.value = false;

    // 延迟清除锚点ID
    setTimeout(() => {
      scrollToId.value = "";
    }, 50);
  }, 300);
};

// 使用选择器和scrollIntoView来滚动，避免使用scrollTop
const ensureScrollToBottom = () => {
  nextTick(() => {
    try {
      // 尝试使用scrollIntoView，它通常比设置scrollTop更可靠
		 uni.pageScrollTo({
		    selector: '#scroll-anchor',
		    duration: 300
		  })
      // const scrollAnchor = document.getElementById('scroll-anchor');
      // if (scrollAnchor) {
      //   // 使用scrollIntoView而不是设置scrollTop
      //   scrollAnchor.scrollIntoView({
      //     behavior: 'auto',
      //     block: 'end'
      //   });
      // } else {
      //   // 回退到使用锚点ID
      //   scrollToBottom(true);
      // }
    } catch (e) {
      console.error('滚动到底部失败', e);
      // 最后的备用方案
      scrollToBottom(true);
    }
  });
};

// 完全简化的未读消息处理
const handleUnreadClick = () => {
  hasUnread.value = false;
  scrollToBottom(true);
};

// 播放语音消息 - 完全按照微信的交互体验实现

const playVoiceMessage = (item) => {
  // 这里是实际的语音播放逻辑
  console.log('播放语音消息', item);
  activeVoice=item
  if(item.isPlaying){
	  innerAudioContext.stop();
	  return
  }
  item.isPlaying=!item.isPlaying
  let voicePath=item.content.msg
  innerAudioContext.src = voicePath;
  innerAudioContext.play();
};

// 修改语音波形动画样式
const styleVoiceWave = {
  width: "120rpx",
  height: "120rpx",
  backgroundImage: 'url("/static/conversation/voice_wave.png")', // 修改路径，移除 @ 符号
  backgroundSize: "contain",
  backgroundRepeat: "no-repeat",
  backgroundPosition: "center",
  marginBottom: "20rpx",
};

// 格式化录音时长
const formatRecordDuration = (duration) => {
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  return `${minutes > 0 ? minutes + "'" : ""}${seconds}"`;
};

// 添加位置选择
const chooseLocation = () => {
  // 改为语音录制功能
  isVoiceMode.value = true;
  activePanel.value = null;
  uni.showToast({
    title: "已切换到语音模式，按住说话",
    icon: "none"
  });
};

// 滚动到顶部时触发加载更多消息
const onScrollToUpper = () => {
  if (isLoadingMore.value || !hasMoreMessages.value) return;

  // 加载更多历史消息
  loadHistoryMessages();
};

// 加载历史消息的函数
const loadHistoryMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value) return;

  isLoadingMore.value = true;

  try {
    // 计算当前页码
    const currentPage = Math.ceil(messageList.value.length / pageSize.value) + 1;

    console.log('加载历史消息，页码:', currentPage, '聊天ID:', chatId.value);

    // 从数据库加载历史消息
    const messages = await getChatMessages(chatId.value, currentPage, pageSize.value);
    console.log('加载的历史消息:', messages);

    // 如果没有更多消息
    if (!messages || messages.length === 0) {
      hasMoreMessages.value = false;
      uni.showToast({
        title: "没有更多消息了",
        icon: "none",
      });
      isLoadingMore.value = false;
      return;
    }

    // 如果返回的消息数量少于页面大小，说明没有更多消息了
    if (messages.length < pageSize.value) {
      hasMoreMessages.value = false;
    }

    // 转换消息格式（注意：历史消息的时间标签需要重新计算）
    const formattedMessages = messages.map((msg) => {
      if (!msg || typeof msg !== 'object') {
        console.error('无效的消息格式:', msg);
        return null;
      }

      const isSelf = String(msg.fromid) === String(uni.getStorageSync('userId'));
      const typecode2 = parseInt(msg.typecode2 || 0);

      // 根据消息类型确定内容类型
      let type = 'text';
      switch(typecode2) {
        case 0: type = 'text'; break;
        case 1: type = 'voice'; break;
        case 2: type = 'image'; break;
        case 3: type = 'video'; break;
        case 4: type = 'forward'; break;
        case 5: type = 'recall'; break;
        default: type = 'text';
      }

      // 解析消息内容
      let content = msg.msg || '';
      try {
        if (typeof content === 'string' && (content.startsWith('{') || content.startsWith('['))) {
          content = JSON.parse(content);
        }
      } catch (e) {
        console.error('解析消息内容失败:', e);
        content = msg.msg || '';
      }

      // 对于历史消息，我们需要重新计算时间标签
      // 这里暂时设为true，稍后会重新计算整个消息列表的时间标签
      const showTimeTag = true;

	  // 处理消息内容，特别是撤回消息
	  let finalContent = content;
	  if (type === 'recall') {
		  // 撤回消息直接使用msg字段的内容
		  finalContent = msg.msg || '消息已撤回';
	  } else if (content && typeof content === 'object' && content.other) {
		  // 其他类型消息如果有other字段则使用other
		  finalContent = content.other;
	  }

      return {
        id: msg.id || Date.now(),
        self: isSelf,
        type: type,
        content: finalContent,
        time: msg.t || new Date().toISOString(),
        // timeTag 和 showTimeTag 会在 recalculateTimeLabels 中统一处理
        timeTag: '', // 初始为空，由recalculateTimeLabels填充
        showTimeTag: false, // 初始为false，由recalculateTimeLabels决定
        avatar: isSelf ? uni.getStorageSync('userInfo').head_img || '/static/My/avatar.jpg' : chatAvatar.value,
        status: 'success',
        unread: false,
		fromid:msg.fromid
      };
    }).filter(Boolean);

    // 将历史消息添加到消息列表的顶部（数据库返回倒序，需要反转）
    messageList.value = [...formattedMessages.reverse(), ...messageList.value];

    // 重新计算整个消息列表的时间标签
    recalculateTimeLabels();

    console.log('历史消息加载完成，当前消息列表长度:', messageList.value.length);

  } catch (error) {
    console.error('加载历史消息失败:', error);
    uni.showToast({
      title: '加载历史消息失败',
      icon: 'none'
    });
  } finally {
    isLoadingMore.value = false;
  }
};

const onMsgAppear = (item) => {
  if (!item.self && item.unread) {
    item.unread = false;
    // 如需通知父组件或后端，可 emit('read', item)
  }
};

// 处理输入框的键盘高度变化事件
const onKeyboardHeightChange = (e) => {
  const height = e.detail?.height || 0;
  console.log("input keyboard height change:", height);

  // 将高度转换为rpx
  keyboardHeight.value = height * 2 + 30;

  // 如果键盘收起，不做任何操作
  if (height == 0) {
    return;
  }

  // 如果键盘弹出，按照微信的逻辑，关闭所有面板
  if (activePanel.value && height > 0) {
    activePanel.value = null;
  }

  // 键盘弹出时，确保滚动到底部
  setTimeout(() => {
    scrollToBottom(true);
  }, 300);
};
let timer=null
const startTimer=()=>{
  recordDuration.value = 0;
  timer = setInterval(() => {
	recordDuration.value += 1;
  }, 1000);
}
const stopTimer=() =>{
  if (timer) {
	clearInterval(timer);
	timer = null;
  }
}
// 处理录音长按和上滑取消
const onTouchMove = (e) => {
  if (!isRecording.value) return;

  const touchMoveY = e.touches[0].clientY;
  const moveDistance = touchStartY.value - touchMoveY;

  // 更灵敏地检测上滑取消，减小阈值
  console.log(moveDistance)
  if (moveDistance > 30) { // 从50减小到30，更容易触发取消
    isCancelRecording.value = true;
	// uni.showToast({ title: '松开手指取消发送', icon: 'none' });
  } else {
    isCancelRecording.value = false;
  }
};

recorderManager.onStart(() => {
  console.log('录音开始');
  startTimer();
});
recorderManager.onStop((res) => {
  console.log('录音结束', res);
  console.log(recordDuration.value)
  console.log('取消状态',isCancelRecording.value)
  console.log(res.tempFilePath)
  isCancelRecording.value = false;
  stopTimer();
  if(recordDuration.value==0)return
  uploadFile(1,res.tempFilePath,2,recordDuration.value)

  recordDuration.value = 0;
});
const startRecordVoice = (e) => {
	touchStartY.value=e.touches[0].clientY
	if (isRecording.value) return;
	isRecording.value = true;
	isCancelRecording.value = true;
	isVoiceMode.value=true
	recorderManager.start({
		format: 'mp3', // 录音格式
		duration: 60000, // 最长录音时间60秒
	});
}

// 取消录音
const stopRecordVoice = () => {
  if (!isRecording.value) return;

  clearInterval(recordTimer.value);

  // #ifdef APP-PLUS || MP-WEIXIN || H5

  recorderManager.stop();
  // #endif

  // #ifndef APP-PLUS || MP-WEIXIN || H5
  uni.stopRecord(); // 停止录音
  // #endif

  isRecording.value = false;

};

// 转发消息
function forwardMessage(item) {
  console.log('转发消息:', item);

  // 检查消息类型是否支持转发
  if (!item || item.type === 'recall') {
    uni.showToast({
      title: '该消息无法转发',
      icon: 'none'
    });
    return;
  }

  // 构建转发内容
  let forwardContent = '';
  switch (item.type) {
    case 'text':
      forwardContent = item.content;
      break;
    case 'image':
      forwardContent = '[图片]';
      break;
    case 'voice':
      forwardContent = '[语音]';
      break;
    case 'video':
      forwardContent = '[视频]';
      break;
    case 'file':
      forwardContent = '[文件]';
      break;
    default:
      forwardContent = '[消息]';
  }

  // 显示转发选项
  uni.showActionSheet({
    // itemList: ['转发给好友', '转发到群聊', '分享到朋友圈'],
    itemList: ['转发给好友'],
    success: (actionRes) => {
      switch (actionRes.tapIndex) {
        case 0:
          // 转发给好友
          forwardToFriend(item);
          break;
        case 1:
          uni.showToast({
            title: '转发到群聊功能开发中',
            icon: 'none'
          });
          break;
        case 2:
          uni.showToast({
            title: '分享到朋友圈功能开发中',
            icon: 'none'
          });
          break;
      }
    }
  });
}

// 转发给好友
function forwardToFriend(messageItem) {
  console.log('转发给好友:', messageItem);
  // 构建要转发的消息数据
  const messageToForward = {
    id: messageItem.id,
    type: messageItem.type,
    content: messageItem.content,
    url: messageItem.url,
    fileName: messageItem.fileName,
    fileSize: messageItem.fileSize,
    duration: messageItem.duration,
    width: messageItem.width,
    height: messageItem.height,
    timestamp: messageItem.timestamp,
    senderName: messageItem.senderName,
    senderAvatar: messageItem.senderAvatar
  };

  // 跳转到转发页面
  uni.navigateTo({
    url: `/pages/forward/index?type=message&message=${encodeURIComponent(JSON.stringify(messageToForward))}&initMessage=${encodeURIComponent(JSON.stringify(messageItem.initParams))}`
  });
}

// 多选消息
function multiSelectMessages() {
  // 这里可以切换到多选模式
  uni.showToast({
    title: "多选功能待实现",
    icon: "none",
    duration: 2000
  });
}

// 打开文件
function openFile(item) {
  if (!item.filePath) {
    uni.showToast({
      title: "文件路径不存在",
      icon: "none"
    });
    return;
  }

  // 尝试打开文件
  // #ifdef APP-PLUS || H5
  uni.openDocument({
    filePath: item.filePath,
    success: function () {
      console.log('文件打开成功');
    },
    fail: function (err) {
      console.error('文件打开失败', err);
      uni.showToast({
        title: "无法打开此类型文件",
        icon: "none"
      });
    }
  });
  // #endif

  // #ifndef APP-PLUS || H5
  uni.showToast({
    title: "当前平台不支持打开文件",
    icon: "none"
  });
  // #endif
}

// 语音通话功能
const startVoiceCall = () => {
  // 关闭面板
  activePanel.value = null;
 // 模拟连接延迟
        setTimeout(() => {
          uni.hideLoading();

          // 打开语音通话界面，确保路径正确
          try {
			let params={
				typecode: 1,
				typecode2: 9,
				fromid: Number(uni.getStorageSync("userId")),
				toId: Number(toId.value),
				msg: '发起语音通话'
			}
			console.log(params)
			sendMessage(params);
            
          } catch (e) {
            console.error('跳转语音通话页面异常', e);
            uni.showToast({
              title: '系统异常，请稍后再试',
              icon: 'none'
            });
          }
        }, 1000);

  // 显示通话界面的弹窗
  // uni.showModal({
  //   title: '语音通话',
  //   content: `是否开始与 ${chatTitle.value} 的语音通话？`,
  //   confirmText: '开始通话',
  //   cancelText: '取消',
  //   success: (res) => {
  //     if (res.confirm) {
  //       // 模拟通话中的状态
  //       uni.showLoading({
  //         title: '正在连接...',
  //         mask: true
  //       });

       
  //     }
  //   }
  // });
};

// 触顶刷新已整合到onScrollToUpper中

// 处理下拉操作
const onPullingDown = (e) => {
  // 可以根据下拉距离显示不同的提示
  console.log("下拉刷新，距离：", e.detail.dy);
};

// 选择视频
const chooseVideo = () => {
  // 关闭面板
  activePanel.value = null;

  uni.chooseVideo({
    sourceType: ['camera'],
    maxDuration: 60, // 最长60秒
    camera: 'back',
    success: (res) => {
      console.log('选择视频成功：', res);

      // 添加视频消息
      messageList.value.push({
        id: Date.now(),
        self: true,
        type: "video",
        content: res.tempFilePath, // 缩略图（这里暂用视频本身路径）
        videoUrl: res.tempFilePath, // 视频路径
        videoDuration: Math.floor(res.duration), // 视频时长（秒）
        thumbUrl: '', // 视频缩略图，可能需要单独生成
        time: new Date().toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        }),
        avatar: "/static/My/avatar.jpg",
        status: "success",
        unread: true,
      });

      // 消息发送后滚动到底部
      nextTick(() => {
        scrollToBottom(true);
      });
    },
    fail: (err) => {
      console.error('选择视频失败:', err);
      uni.showToast({
        title: '选择视频失败',
        icon: 'none'
      });
    }
  });
};

const uploadFile = (typecode2,filePath,type,duration)=>{
	uni.uploadFile({
		url: `http://43.198.105.182:82/api/upload?type=${type}`,
		filePath: filePath,
		name: 'file',
		header:{
			'x-token': uni.getStorageSync("token") || '',
		},
		// formData: {
		// 	'file': tempFilePaths[0]
		// },
		success: (uploadFileRes) => {
			console.log(uploadFileRes.data);
			let res=JSON.parse(uploadFileRes.data)
			let msg=res.data.filepath
			if(typecode2==1){
				msg=JSON.stringify({msg,duration})
			}
			// const {name}=userInfo.value
			sendTextMessage(typecode2,msg)
			// setMeInfo({name,header_img}).then(res=>{
			// 	userInfo.value.head_img=header_img
			// 	uni.setStorageSync("userInfo", userInfo.value);
			// })
		}
	});
}
const chooseImage = (typecode2) => {
	let sourceType='album'
	let type=0
	let action='chooseImage'
	if(typecode2==2){
		sourceType='album'
		action='chooseImage'
		type=0
	}
	if(typecode2==3){
		sourceType='camera'
		action='chooseVideo'
		type=1
	}
	console.log('sourceType',sourceType)
	uni[action]({
		maxDuration:60,
		sourceType: [sourceType],
		success: (chooseImageRes) => {

			const tempFilePaths =typecode2==2? chooseImageRes.tempFilePaths:[chooseImageRes.tempFilePath];
			tempFilePaths.forEach(item=>{

				uploadFile(typecode2,item,type)
			})

		},
		fail:res=>{
			console.log(res)
		}
	});
};
// 打开视频全屏预览
const openVideoFullscreen = (item) => {
	console.log(item)
  if (!item) {
    uni.showToast({
      title: '无效的视频',
      icon: 'none'
    });
    return;
  }

  currentVideo.value = item.content;
  // currentVideoPoster.value = item.thumbUrl || '';
  showVideoFullscreen.value = true;

  // 延迟一下再播放，确保视频元素已渲染
  setTimeout(() => {
	try{
		console.log(1)
		const videoContext = uni.createVideoContext('fullscreen-video');
		console.log(11)
		if (videoContext) {
			console.log(12)
		  videoContext.play();
		  videoContext.requestFullScreen({ direction: 0 });
		}
	}catch(e){
		console.log(e)
	}

  }, 300);
};

// 关闭视频全屏预览
const closeVideoFullscreen = () => {
  const videoContext = uni.createVideoContext('fullscreen-video');
  if (videoContext) {
    try {
      videoContext.exitFullScreen();
    } catch (e) {
      console.error('退出全屏失败', e);
    }

    // 停止播放
    videoContext.stop();
  }

  showVideoFullscreen.value = false;
  currentVideo.value = '';
  currentVideoPoster.value = '';
};

// 处理视频全屏变化事件
const handleVideoFullscreenChange = (e) => {
  console.log('视频全屏状态变化:', e);
  // 如果退出全屏，则关闭视频预览
  if (!e.detail.fullScreen) {
    closeVideoFullscreen();
  }
};

// 处理视频播放结束事件
const handleVideoEnded = (e) => {
  const videoContext = uni.createVideoContext(e.target.id);
  videoContext.seek(0);
  // 不自动播放，而是显示封面
  // videoContext.pause();
};
// 复制消息
const onCopyMsg = (item) => {
  console.log('复制消息:', item);

  if (!item || item.type !== 'text') {
    uni.showToast({
      title: '只能复制文本消息',
      icon: 'none'
    });
    return;
  }

  // 复制文本内容到剪贴板
  uni.setClipboardData({
    data: item.content,
    success: () => {
      /* uni.showToast({
        title: '复制成功',
        icon: 'success'
      }); */
    },
    fail: () => {
      /* uni.showToast({
        title: '复制失败',
        icon: 'none'
      }); */
    }
  });
};

// 删除消息
const onDeleteMsg = (item) => {
  console.log('删除消息:', item);

  uni.showModal({
    title: '删除消息',
    content: '确定要删除这条消息吗？',
    confirmText: '删除',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          // 从数据库删除消息
          await deleteMessage(item.id);

          // 从消息列表中移除
          const messageIndex = messageList.value.findIndex(msg => msg.id === item.id);
          if (messageIndex !== -1) {
            messageList.value.splice(messageIndex, 1);
          }

          /* uni.showToast({
            title: '删除成功',
            icon: 'success'
          }); */
        } catch (error) {
          console.error('删除消息失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 撤回消息
const onRecallMsg = (item) => {
  console.log('撤回消息:', item);

  if (!item.self) {
    uni.showToast({
      title: '只能撤回自己的消息',
      icon: 'none'
    });
    return;
  }

  // 检查消息时间是否在2分钟内
  const messageTime = new Date(item.time);
  const currentTime = new Date();
  const timeDiff = currentTime.getTime() - messageTime.getTime();
  const twoMinutes = 2 * 60 * 1000; // 2分钟的毫秒数

  if (timeDiff > twoMinutes) {
    uni.showToast({
      title: '超过2分钟无法撤回',
      icon: 'none'
    });
    return;
  }

  uni.showModal({
    title: '撤回消息',
    content: '确定要撤回这条消息吗？',
    confirmText: '撤回',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          console.log('开始撤回消息流程...');

          // 1. 发送撤回消息到服务器
          const recallParams = {
            fromid: uni.getStorageSync('userId'),
            toId: Number(toId.value),
            msg: JSON.stringify({
              ret: item.id, // 被撤回的消息ID
              other: '你撤回了一条消息'
            }),
            typecode: sourceTypecode.value ? 2 : 1,
            typecode2: 5, // 撤回消息类型
          };

          console.log('发送撤回消息参数:', recallParams);

          // 发送撤回消息到服务器
          await sendMessage(recallParams);
          console.log('撤回消息已发送到服务器');

          // 2. 立即更新本地数据库中的原消息状态
          await recallMessage(item.id, item);
          console.log('本地数据库已更新');

          // 3. 立即更新消息列表中的消息状态
          const messageIndex = messageList.value.findIndex(msg => msg.id == item.id);
          if (messageIndex !== -1) {
            messageList.value[messageIndex] = {
              ...messageList.value[messageIndex],
              type: 'recall',
              content: '你撤回了一条消息',
              recalled: true
            };
            console.log('消息列表已更新');
          }

         /*  uni.showToast({
            title: '撤回成功',
            icon: 'success'
          }); */
        } catch (error) {
          console.error('撤回消息失败:', error);
          uni.showToast({
            title: '撤回失败，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 处理播放视频
const handlePlayVideo = (item) => {
  openVideoFullscreen(item);
};

// 查看图片
const viewImage = (item) => {
  // 简单的图片预览实现
  uni.previewImage({
    urls: [item.content], // 需要预览的图片http链接列表
    current: item.content // 当前显示图片的http链接
  });
  // 如果 MessageList 菜单是打开的，关闭它
  if (messageListComponent.value && typeof messageListComponent.value.closeLocalBubbleMenu == 'function') {
    messageListComponent.value.closeLocalBubbleMenu();
  }
};

// 导出方法
defineExpose({
  scrollToBottom,
  sendTextMessage,
  receiveMessage,
  onKeyboardHeightChange,
  handlePlayVideo
});

// 当输入高度变化时更新容器高度
watch(inputTotalHeight, () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const screenHeight = systemInfo.windowHeight;
    const statusBarHeightPx = systemInfo.statusBarHeight;
    const navBarHeightPx = uni.upx2px(navBarHeight.value);
    const inputHeightPx = uni.upx2px(inputTotalHeight.value);

    // 设置为确切的像素值，避免使用calc()
    containerHeight.value = `${screenHeight - statusBarHeightPx - navBarHeightPx - inputHeightPx}px`;
  } catch (e) {
    console.error('更新容器高度失败', e);
  }
});


</script>

<style lang="scss" scoped>
.chat-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 状态栏样式 */
.status_bar {
  background-color: white;
}

/* 固定顶部导航栏容器 */
.fixed-header {
  height: 88rpx; // 44px -> 88rpx
  overflow: hidden;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; // 44px -> 88rpx
  background-color: #fff;
  position: relative;
  padding: 0 10rpx;
  box-sizing: border-box;

  .nav-left,
  .nav-right {
    width: 88rpx;
    height: 88rpx; // 44px -> 88rpx
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    box-sizing: border-box;

    .nav-icon-img {
      width: 44rpx;
      height: 44rpx;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    line-height: 88rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

/* 聊天区域 */
.chatslist_container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-anchor: auto;
  scroll-behavior: auto;
  -webkit-overflow-scrolling: touch;
  background-color: #f5f5f5;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 输入区域样式 */
.input-wrapper-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: #f5f5f5;
  transition: bottom 0.25s ease-out;
}

.toolbar {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  border-top: 1rpx solid #eaeaea;
}

/* 语音按钮、表情按钮、更多按钮 */
.voice-button,
.emoji-button,
.more-button,
.send-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.voice-button.active,
.emoji-button.active,
.more-button.active {
  background-color: #e0e0e0;
  border-radius: 8rpx;
}

.tool-icon-img {
  width: 44rpx;
  height: 44rpx;
}

.send-icon-img {
  width: 44rpx;
  height: 44rpx;
}

/* 输入框样式 */
.input-wrapper {
  flex: 1;
  margin: 0 16rpx;
  transition: all 0.3s;
}

.input-wrapper.expanded {
  margin-right: 8rpx;
}

.message-input {
  width: 100%;
  min-height: 72rpx;
  background-color: #fff;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 语音输入按钮 */
.voice-input-button {
  width: 100%;
  height: 72rpx;
  background-color: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-input-button text {
  font-size: 28rpx;
  color: #666;
}

.voice-input-button:active {
  background-color: #e0e0e0;
}

/* 扩展面板 */
.extension-panel {
  position: relative;
  left: 0;
  right: 0;
  background-color: #f5f5f5;
  z-index: 1;
  border-top: none !important;
  margin: 0;
  padding: 0;
}

/* 表情面板 */
.emoji-panel {
  width: 100%;
  height: 500rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  border-top: 1rpx solid #eaeaea;
  overflow: hidden;
}

.emoji-scroll {
  flex: 1;
  width: 100%;
  height: 420rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.emoji-item {
  width: calc(100% / 7);
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 44rpx;
}

/* 表情面板底部发送区 */
.emoji-send-area {
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  border-top: 1rpx solid #e0e0e0;
  background-color: #f5f5f5;
}

.emoji-send-button {
  width: 120rpx;
  height: 60rpx;
  background-color: #cccccc;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.emoji-send-button.active {
  background-color: #07c160;
}

.emoji-backspace {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.emoji-delete-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 更多功能面板 */
.more-panel {
  width: 100%;
  height: 250rpx;
  background-color: #f8f8f8;
  padding: 30rpx 20rpx;
  border-top: 1rpx solid #eaeaea;
  box-sizing: border-box;
  overflow-y: auto;
}

.more-panel-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.more-panel-item {
  width: 25%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.more-panel-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.more-icon-img {
  width: 56rpx;
  height: 56rpx;
}

.more-panel-title {
  font-size: 24rpx;
  color: #666;
}

/* 语音输入面板 */
.voice-input-panel {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 100rpx;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  z-index: 101;
}

.voice-recording-indicator {
  width: 300rpx;
  height: 300rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.recording-wave-animation {
  width: 120rpx;
  height: 120rpx;
  background-image: url("/static/conversation/voice_wave.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 20rpx;
  animation: wave 1s infinite;
}

.cancel-recording-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.cancel-icon {
  width: 80rpx;
  height: 80rpx;
}

@keyframes wave {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.voice-duration {
  font-size: 40rpx;
  color: #fff;
  margin-bottom: 20rpx;
}

.voice-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 激活状态样式 */
.active {
  color: #ff4f81;
}

.unread-dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 120rpx; // 距离底部输入区上方
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  padding: 8rpx 24rpx;
  display: flex;
  align-items: center;
  z-index: 20;
  font-size: 26rpx;
  color: #ff4f81;

  .dot {
    width: 16rpx;
    height: 16rpx;
    background: #ff4f81;
    border-radius: 50%;
    margin-right: 12rpx;
  }
}

.msg-unread-dot {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4f81;
  border-radius: 50%;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 129, 0.15);
}

/* 下拉加载提示 */
.loading-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f2f2f2;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 无更多消息提示 */
.no-more-messages {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 下拉刷新样式 */
.refresher-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
  border: 3rpx solid #f1f1f1;
  border-radius: 50%;
}

.refresher-dots {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
}

.refresher-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
  margin: 0 5rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes breath {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }
}

/* 新增呼吸灯效果 */
.breathing-dot {
  animation: breath 1.5s infinite ease-in-out;
}

/* 视频全屏预览样式 */
.fullscreen-video-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fullscreen-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-video-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.close-video-icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
}

/* 新增性能优化样式 */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto;
  will-change: transform;
  transform: translateZ(0);
}

.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

.message-item {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  position: relative;
  z-index: 1;
}
</style>
